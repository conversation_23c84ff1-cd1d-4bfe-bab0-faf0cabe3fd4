import { Package } from "@/types/package";
import PackageCard from "./card";

interface PackageCardsProps {
  packages: Package[];
}

const PackageCards: React.FC<PackageCardsProps> = ({ packages }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
      {packages.map((pkg) => (
        <PackageCard key={pkg.id} package={pkg} />
      ))}
    </div>
  );
};

export default PackageCards;