"use client"

import React, { useState, useEffect } from "react";

export const ScrollSpyTabs = ({ sectionIds }: { sectionIds: { id: string, label: string }[] }) => {
  const [active, setActive] = useState(sectionIds[0].id)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const visible = entries.find(entry => entry.isIntersecting)
        if (visible) setActive(visible.target.id)
      },
      { threshold: 0.1 }
    )

    sectionIds.forEach(sec => {
      const el = document.getElementById(sec.id)
      if (el) observer.observe(el)
    })

    return () => observer.disconnect()
  }, [sectionIds])

  const scrollTo = (id: string) => {
    document.getElementById(id)?.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }

  return (
    <div className="sticky top-35 md:top-31 z-50 bg-white border-b mb-5">
      <div
        className="flex space-x-1 md:space-x-4 overflow-x-scroll scroll-smooth px-4 py-2"
      >
        {sectionIds.map(sec => (
          <button
            key={sec.id}
            onClick={() => scrollTo(sec.id)}
            className={`px-3 py-1 rounded-md whitespace-nowrap transition ${active === sec.id ? 'bg-brand text-white' : 'text-gray-600 hover:bg-secondary'
              }`}
          >
            {sec.label}
          </button>
        ))}
      </div>
    </div >
  )
}
