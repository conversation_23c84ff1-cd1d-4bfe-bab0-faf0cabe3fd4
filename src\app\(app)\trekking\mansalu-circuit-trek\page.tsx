'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import Gallery<PERSON>ithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedManasluCircuitItinerary } from '@/data/trekking/mansalu-trek/itinerary-detailes'
import { manasluCircuitSection } from '@/data/trekking/mansalu-trek/package-info'

const MansaluCircuitTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Machhakhola - Kathmandu",
        accommodation: "Tea Houses",
        duration: "Approx 6-7 hrs daily/Can vary",
        maxElevation: "Larke La Pass (5,106 m)",
        group: "2–10 max",
        region: "Manaslu Region",
        type: "Unpaved and muddy",
        bestSeason: "March–May & September–November",
        grade: "Moderate – Difficult",
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Enjoy the panoramic view of mountains like Annapurna, Manaslu, Hiunchuli, and more" },
        { text: "Cross one of the highest mountain passes in Nepal – Larke La (5,106 m)" },
        { text: "Explore the traditional culture of remote Himalayan settlements like Sama Gaon and Samdo" },
        { text: "Trek through lush forests, see diverse wildlife, and experience unique vegetation" },
        { text: "Witness majestic peaks like Ngadi Chuli, Himalchuli, Ganesh Himal along the trail" },
        { text: "Experience a blend of Nepalese and Tibetan culture with monasteries and prayer flags" },
    ];

    const content = {
        paragraphs: [
            `Manaslu circuit trek is a very famous and less crowded trek in Nepal that circles Manaslu Peak, the eight highest Mountain in the world. The trail takes you through remote villages, deep river valleys, lush forests, wildlifes, and high mountain passes, including the challenging Larke Pass at 5,160 meters. Manaslu circuit trek offers a perfect mix of natural beauty, cultural richness, and adventure, with views of Manaslu, Himlung, and other Himalayan peaks. This trek is ideal for those seeking a quieter alternative to the Annapurna or Everest regions.

            The Manaslu Circuit Trek also gives you a deep cultural experience as you pass through Gurung and Tibetan-style villages with old monasteries, prayer flags, and Buddhist traditions. The trail goes through many types of landscapes, from green forests to high mountain areas, making it a great choice for both nature and culture lovers.
        `,
        ],
    };

    const note = {
        paragraphs: [
            `This trek reaches high altitudes above 5,000 meters, so proper acclimatization is essential to prevent altitude sickness. Make sure to follow your guide’s instructions and stay hydrated.`,

            `Some sections of the trail can be steep, narrow, or affected by landslides depending on the season. It is recommended to bring proper trekking shoes and gear.`,

            `In winter, the pass may be blocked by snow. Spring and Autumn are the safest and most popular seasons for this trek.`,
        ],
    };

    const briefing = {
        paragraphs: [
            `Before your departure, we will organize a short online or in-person briefing session where you’ll meet your trek leader and get insights into the itinerary, gear checklist, altitude tips, and cultural dos and don’ts. This ensures a well-informed and safe trekking experience.`,
        ],
    };



    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional guide and porters including their food, accommodation, insurance, and wages.",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 Meals per day including Breakfast, Lunch, and Dinner.",
                "Medicines and water purifiers will be provided.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All necessary permits including MRAP, MCAP, ACAP and TIMS card as required.",
            ],
        },
        {
            title: "Medical & Safety",
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                "First aid kit and basic medical supplies throughout the trek.",
            ],
        },
        {
            title: "Camping & Equipment",
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                "Basic equipment required for the trek (provided by the company).",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government and service taxes included.",
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink not included in the provided 3 meals per day.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal trekking equipment (sleeping bag, trekking shoes, headlamp, crampons, etc.).",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the guide and porter.",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additional costs due to unforeseen circumstances (illness, weather, natural calamities, etc.).",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses not explicitly mentioned in the included section.",
            ],
        },
    ];

    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,324 m)" },
        { day: 2, title: "Drive from Kathmandu to Machha Khola (870 m) [~8–10 hrs drive, ↓ 454 m]" },
        { day: 3, title: "Trek to Jagat (1,300 m) [22 km, ~7 hrs, ↑ 430 m]" },
        { day: 4, title: "Jagat → Deng (1,800 m) [~20 km, ~6–7 hrs, ↑ 500 m]" },
        { day: 5, title: "Deng → Namrung (2,660 m) [~19.5 km, ~6–7 hrs, ↑ 860 m]" },
        { day: 6, title: "Namrung → Samagaon (3,530 m) [~17.4 km, ~6–7 hrs, ↑ 890 m]" },
        { day: 7, title: "Acclimatization day at Samagaon" },
        { day: 8, title: "Samagaon → Samdo (3,690 m) [~16.4 km, ~4–5 hrs, ↑ 160 m]" },
        { day: 9, title: "Samdo → Dharamsala (4,460 m) [~11.7 km, ~5–6 hrs, ↑ 770 m]" },
        { day: 10, title: "Dharamsala → Larke La Pass (5,106 m) → Bhimtang (3,720 m) [~24.7 km, ~8–9 hrs, ↓ 740 m]" },
        { day: 11, title: "Bhimtang → Dharapani (1,860 m) [~20 km, ~7–8 hrs, ↓ 1,860 m]" },
        { day: 12, title: "Drive back to Kathmandu (1,324 m) [~8–10 hrs, ↓ 536 m]" },
        { day: 13, title: "Final Departure from Kathmandu" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Emily Carter",
            role: "Solo Trekker",
            company: "Backpackers UK",
            rating: 5,
            content:
                "The Manaslu Circuit was everything I hoped for—raw, untouched, and incredibly scenic. Larke Pass was a real challenge but so rewarding. Our guide made the entire experience safe and unforgettable.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Tashi Lama",
            role: "Trekking Enthusiast",
            company: "Nepal Trails",
            rating: 5,
            content:
                "This trail is peaceful compared to Everest or Annapurna. The cultural mix of Tibetan and Gurung villages is beautiful. Samagaon and Samdo felt like stepping into another world.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Hanna Müller",
            role: "Photographer",
            company: "Hike & Snap",
            rating: 4,
            content:
                "Absolutely stunning scenery and diverse landscapes. Some sections were tough, especially Larke Pass, but every view and village made it worth it. Highly recommend going in autumn for clear skies.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Suman Karki",
            role: "Nature Lover",
            company: "Nepal Explorer",
            rating: 5,
            content:
                "This trek had it all—forests, rivers, high passes, and kind locals. Our guide took great care of us, especially during the high-altitude days. Truly a life-changing adventure!",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Lina Chen",
            role: "Travel Blogger",
            company: "WanderWithLina",
            rating: 5,
            content:
                "Manaslu was magical. Less crowded, authentic, and visually incredible. The tea houses were basic but cozy, and the cultural immersion was a highlight. I’ll never forget the sunrise at Bhimtang.",
            avatar: "/images/review/5.png",
        },
    ];



    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Manaslu Circuit Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        Manaslu Circuit Trek is a very famous and less crowded trek in Nepal that circles Manaslu Peak, the eighth highest mountain in the world. The trail takes you through remote villages, deep river valleys, lush forests, wildlife, and high mountain passes, including the challenging Larke Pass at 5,160 meters. Manaslu Circuit Trek offers a perfect mix of natural beauty, cultural richness, and adventure, with views of Manaslu, Himlung, and other Himalayan peaks. This trek is ideal for those seeking a quieter alternative to the Annapurna or Everest regions.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Mansalu Circuit Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedManasluCircuitItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/map/trekking/mansalu-circuit-trek.webp"
                            altText="Mansalu Circuit Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={manasluCircuitSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default MansaluCircuitTrek
