interface Activity {
  id: string
  name: string
  slug: string
  createdAt: string
  updatedAt: string
}

interface Region {
  id: string
  name: string
  slug: string
  createdAt: string
  updatedAt: string
}

interface Highlights {
  id: string
  packageId: string
  title: string
  description: string
  createdAt: string
  updatedAt: string
}

interface Description {
  id: string
  packageId: string
  title: string
  description: string
  createdAt: string
  updatedAt: string
}

interface ShortItinerary {
  id: string
  packageId: string
  title: string
  points: string[]
  createdAt: string
  updatedAt: string
}

interface PackageGalleryImage {
  image: string
  alt?: string
}

interface Gallery {
  id: string
  packageId: string
  title: string
  createdAt: string
  updatedAt: string
  PackageGalleryImage: PackageGalleryImage[]
}

interface YtVideo {
  id: string
  packageId: string
  title: string
  links: string[]
  createdAt: string
  updatedAt: string
}

interface Inclusions {
  id: string
  packageId: string
  title: string
  details: string
  createdAt: string
  updatedAt: string
}

interface Exclusions {
  id: string
  packageId: string
  title: string
  details: string
  createdAt: string
  updatedAt: string
}

interface Map {
  id: string
  packageId: string
  title: string
  map: string
  createdAt: string
  updatedAt: string
}

interface InfoItem {
  id: string
  packageId: string
  title: string
  createdAt: string
  updatedAt: string
}

interface Info {
  id: string
  packageId: string
  title: string
  createdAt: string
  updatedAt: string
  items: InfoItem[]
}

interface Itinerary {
  id: string
  packageId: string
  heading: string
  day: number
  title: string
  activity: string
  trekDistance: string
  flightHours: string
  drivingHours: string
  highestAltitude: string
  trekDuration: string
  image: string
  createdAt: string
  updatedAt: string
}

interface Equipment {
  id: string
  packageId: string
  title: string
  description: string
  head: string
  body: string
  face: string
  createdAt: string
  updatedAt: string
}

interface CostDate {
  id: string
  packageId: string
  days: string
  startDate: string
  endDate: string
  price: string
  discountPrice: string
  tripStatus: string
  published: boolean
  markUpcomingTreks: boolean
  createdAt: string
  updatedAt: string
}

interface GroupPrice {
  id: string
  packageId: string
  numberOfPeople: string
  pricePerPerson: string
  note: string
  published: boolean
  createdAt: string
  updatedAt: string
}

interface FAQ {
  id: string
  packageId: string
  question: string
  answer: string
  published: boolean
  createdAt: string
  updatedAt: string
}

interface Review {
  id: string
  packageId: string
  name: string
  email: string
  rating: number
  comment: string
  reviewImage: string
  published: boolean
  createdAt: string
  updatedAt: string
}

interface PackageDetail {
  id: string
  name: string
  slug: string
  activityId: string
  regionId: string
  accomodation: string
  distance: string
  type: string
  duration: string
  altitude: string
  meals: string
  groupSize: string
  price: string
  discountPrice: string
  bestSeason: string
  transport: string
  activityPerDay: string
  grade: string
  bookingLink: string
  overviewDescription: string
  thumbnail: string
  mainImage: string
  mainImageAlt: string
  pdfBrochure: string
  published: boolean
  tripOftheMonth: boolean
  popularTour: boolean
  shortTrek: boolean
  createdAt: string
  updatedAt: string
  activity: Activity
  region: Region
  highlights: Highlights
  description: Description
  shortItinerary: ShortItinerary
  gallery: Gallery
  ytVideo: YtVideo
  inclusions: Inclusions
  exclusions: Exclusions
  map: Map
  info: Info
  itinerary: Itinerary[]
  equipment: Equipment[]
  costDate: CostDate[]
  groupPrice: GroupPrice[]
  faq: FAQ[]
  review: Review[]
}

interface ApiResponse {
  statusCode: number
  success: boolean
  data: PackageDetail
  path: string
  message: string
  meta: object
}






// export interface IPackage {
//   id: string | number
//   slug: string
//   title: string
//   image: string
//   duration: string
//   days: number
//   currency: string
//   originalPrice: number
//   currentPrice: number
//   priceUnit: string
//   rating: number
//   difficulty: string
// }

// export interface Package {
//   id: number;
//   slug: string;
//   title: string;
//   duration: string;
//   days: number;
//   originalPrice: number;
//   currentPrice: number;
//   currency: string;
//   priceUnit: string;
//   rating: number;
//   image: string;
//   difficulty: 'Easy' | 'Moderate' | 'Hard'
//   link?: string;
// }
