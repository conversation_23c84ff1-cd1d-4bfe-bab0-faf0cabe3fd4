'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedPokharaFourDaysTrailRunningItinerary } from '@/data/trailrunning/pokhara-4-days/itinerary-detailed'
import { pokharaTrailSections } from '@/data/trailrunning/pokhara-4-days/package-info'

const PokharaFourDaysTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Hemja - Pokhara',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 5–6 hrs daily / Can vary',
        maxElevation: '2500 m',
        group: '6',
        region: 'Annapurna Region',
        type: 'Paved and unpaved paths',
        bestSeason: 'Spring and Autumn',
        grade: 'Easy (with some technical sections)'
    }

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Explore the traditional villages with their unique culture and way of living.' },
        { text: 'Explore Lwang village, which is very famous for growing tea.' },
        { text: 'Beautiful view of mountains and amazing landscapes.' },
        { text: 'Run through the lush forest with the chirping sound of birds.' },
    ]

    const content = {
        paragraphs: [
            `Set off for the most thrilling adventure of your life - Trail Running. This 4 Days race carries you through terrains filled with astonishing landscapes, mountain views, and traditional Gurung villages. The trail is mixed up with several ascents and descents. Sometimes requiring a little push on a few technical trails. 

            The spectacular views of Annapurna South (7,219 m | 23,684 ft) and Machhapuchhre mountains (6,993 m | 22943 ft) from the viewpoint while racing gives you a reason to run further. Sipping locally brewed tea from the garden of Lwang with the morning view of majestic mountains is a pure joy. Staying at family-operated lodges gives you a family-like environment with the opportunity to interact with locals.

            Trail running begins from Hemja (1,164 m/ 3,819 ft) with a gradual up leading to Dhampus Village. This is a warm-up for a run of a day. You reach Lwang via Dhampus, Pothana, and Pitam Deurali. Day 2 is a few ascents and more descents through the dense tranquility of dense forests to Landruk village.

            Day 3 offers an intense route through terraced fields, natural streams, and picturesque villages, ending at Panchase Top. The panoramic view of Pokhara Valley, mountains, magical sunrise, and sunset is your reward for the day. The final day is a descent to the shore of Fewa Lake. The race wraps at 822 m. with rowing a wooden boat to Pohara.

            `,
        ],
    }

    const note = {
        paragraphs: [
            `The Manaslu Circuit Trail begins with an 8–9 hour drive from Kathmandu to Machha Khola. Arrive in Kathmandu at least a day before to prepare.`,
            `This trail running involves challenging terrains above 5000 meters, suitable for those experienced in high-altitude treks.`,
        ],
    }

    const briefing = {
        paragraphs: [
            `After confirming your booking, we'll hold an online briefing to explain itinerary details, safety measures, and necessary preparations.`,
        ],
    }


    const myInclusions: Category[] = [
        {
            title: 'Guide & Staff',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.',
                'Porter for each 2 person',
            ],
        },
        {
            title: 'Meals & Drinks',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                'We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
                'Water bottle to store hot water overnight.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All the necessary paperwork and Annapurna conservation entry permit ( ACAP permit & TIMS card etc.)',
            ],
        },
        {
            title: 'Medical & Safety',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with an oximeter.',
            ],
        },
        {
            title: 'Extras',
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                'Trek achievement certificate after the trek.',
                'Government taxes, TDS, VAT & other legal documents.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Personal Expenses',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any other food/drink should be covered by you, except (3 meals) provided by the company.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Personal equipments'],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: ['Tips for the Guide and Potter'],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: ['Any expenses that are not in the included section.'],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Hemja → Lwang via Dhampus and Pitam Deurali (1,700 m) [↑ 603 m, ~23 km, 4–5 hrs]" },
        { day: 2, title: "Lwang → Landruk via Dhod Kharka and Kuypuche Basti (1,640 m) [↓ 60 m, 11 km, ~3 hrs]" },
        { day: 3, title: "Landruk → Panchase Top via Pothana and Bhadure (2,500 m) [↑ 860 m, ~25 km, 5–6 hrs]" },
        { day: 4, title: "Panchase Top → Pokhara (822 m) [↓ 1,678 m, 21 km, 4–5 hrs]" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Anita Rai",
            role: "Yoga Instructor",
            company: "Pokhara Wellness Collective",
            rating: 5,
            content:
                "The four-day run around Pokhara was pure bliss. Gentle trails, warm village hospitality, and epic mountain views made every kilometer a joy. Highly recommend for beginners!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Lucas Jensen",
            role: "Trail Running Coach",
            company: "Nordic Runners Club",
            rating: 5,
            content:
                "An ideal intro to Himalayan trail running. The flowy forest trails, peaceful Panchase ridge, and lakeside finish were all thoughtfully organized. Great guides, too!",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Meena Thapa",
            role: "Outdoor Enthusiast",
            company: "RunNepal Community",
            rating: 4,
            content:
                "I loved every part of this journey—the tea breaks in Lwang, the sunrise over Annapurna, and the wooden boat ride across Fewa Lake. Just wish I packed a better headlamp!",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "David Lin",
            role: "Travel Photographer",
            company: "WanderShots",
            rating: 5,
            content:
                "A photographer’s dream! Lush trails, authentic village life, and dramatic ridge lines—all in just four days. Can’t wait to come back with friends.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Pema Lama",
            role: "Fitness Coach",
            company: "Everest Fit Lab",
            rating: 5,
            content:
                "As a coach, I loved how manageable yet fulfilling this trail was. Enough challenge to push your limits, but not overwhelming. Perfect first Himalayan experience.",
            avatar: "/images/review/5.png",
        },
    ]

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image4.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="4 Day Pokhara Trail Running"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is at an easy level with little uphill climb. If you are healthy and physically fit, you will easily be able to complete this program. The accommodations in this area are decent with facilities available.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Pokhara Trail Running – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedPokharaFourDaysTrailRunningItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="4 Day Pokhara Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={pokharaTrailSections} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default PokharaFourDaysTrailRunning