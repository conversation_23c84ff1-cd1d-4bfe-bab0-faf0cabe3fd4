'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import { detailedItinerary } from '@/data/itinerarydata'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import PreperationandTraining from '@/modules/fastpacking/components/training'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { Bed, Car, Coffee, FileText, PlaneIcon, User } from 'lucide-react'
import { sections } from '@/data/package-info'

const AnnapurnaCircuitFastpackingPage = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Nepal',
        accommodation: 'Lodge',
        duration: '8 Days',
        maxElevation: '5106 m at the Thorung La Pass',
        group: '8',
        region: 'Annapurna Region',
        mealsIncluded: '(Breakfast, Lunch, and Dinner) during the trek',
        bestSeason: 'March to May and September to November',
        grade: 'Challenging'
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Vast green valleys and forests of the Dhorpatan Hunting Reserve (1,325 sq km).' },
        { text: 'Spot snow leopards, musk deer, blue sheep, and red panda.' },
        { text: 'Authentic villages like Gurja Khani and Magar settlements.' },
        { text: 'Experience the culture of the Gurung people.' },
        { text: 'Visit the sacred Muktinath Temple.' },
        { text: 'Cross the Thorung La Pass, the highest point of the trek.' },
        { text: 'Explore the traditional villages of Manang and Mustang.' },
        { text: 'Witness the annual Mani Rimdu festival in Jomsom.' },
    ]

    const content = {
        paragraphs: [
            `The Dhorpatan Trek takes you deep into west Nepal’s untouched wilderness, traversing the only hunting reserve…`,
        ],
    }

    const note = {
        paragraphs: [
            `Dhorpatan Trek starts with a 6–7 hours bus ride from Kathmandu to Pokhara (approx. 200 km), but you can fly…`,
            `To stay on track with your adventure, we advise arriving in Kathmandu at least one day before the trek…`,
        ],
    }

    const briefing = {
        paragraphs: [
            `After confirming your booking and receiving your essential documents, we'll invite you to an online trip briefing…`,
        ],
    }


    const myInclusions: Category[] = [
        {
            title: 'Transportation',
            icon: <Car className="w-5 h-5 text-dark" />,
            items: ['Kathmandu ↔ Pokhara ↔ Kathmandu on tourist bus', 'Airport transfers'],
        },
        {
            title: 'Accommodations',
            icon: <Bed className="w-5 h-5 text-dark" />,
            items: ['Tea-house lodges every night of the trek', '2 nights hotel in Pokhara (B&B)'],
        },
        {
            title: 'Food',
            icon: <Coffee className="w-5 h-5 text-dark" />,
            items: [
                '3 meals per day on trek (B, L, D)',
                'Tea & coffee twice daily',
                'Fresh fruit each evening',
            ],
        },
        {
            title: 'Guides & Porters',
            icon: <User className="w-5 h-5 text-dark" />,
            items: [
                'Experienced trekking guides',
                'Porters to carry your gear',
                'Support crew for logistics',
            ],
        },

    ]

    const myExclusions: Category[] = [
        {
            title: 'International Flight',
            icon: <PlaneIcon className="w-5 h-5 text-dark" />,
            items: ['International airfare & visa fee'],
        },
        {
            title: 'Kathmandu Hotel',
            icon: <Bed className="w-5 h-5 text-dark" />,
            items: [
                'Accommodation in Kathmandu before/after trek',
                'Extra-night hotel stays due to early/late flights',
            ],
        },
        {
            title: 'Food in Kathmandu',
            icon: <Coffee className="w-5 h-5 text-dark" />,
            items: [
                'All meals in Kathmandu',
                'Lunch & dinner in Pokhara',
            ],
        },
        {
            title: 'Guide & Porter Tips',
            icon: <User className="w-5 h-5 text-dark" />,
            items: ['Tips for guides & porters'],
        },
        {
            title: 'Visa & Permits',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All required trek permits & TIMS card',
                'Government taxes and entry fees',
            ],
        },
    ]

    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Drive Ktm → Pokhara (820 m), approx 7 hrs." },
        { day: 2, title: "Drive Pokhara → Nayapul… trek to Ulleri (1 540 m), approx 4 hrs." },
        { day: 3, title: "Trek Ulleri → Ghorepani (2 860 m), approx 4 hrs." },
        { day: 4, title: "Poon Hill (3 210 m) → Tadapani/Chule, approx 4 hrs." },
        { day: 5, title: "Tadapani/Chule → Chomrong/Sinuwa, approx 5 hrs." },
        { day: 6, title: "Chomrong/Sinuwa → Himalaya/Deurali, approx 6–7 hrs." },
        { day: 7, title: "Himalaya/Deurali → ABC (4 130 m), approx 5 hrs." },
        { day: 8, title: "ABC → Bamboo (2 350 m), approx 6 hrs." },
        { day: 9, title: "Bamboo → Jhinu Danda (1 780 m), approx 4 hrs." },
        { day: 10, title: "Jhinu Danda → Ghandruk Phedi → Pokhara, approx 3 hrs drive." },
        { day: 11, title: "Drive Pokhara → Kathmandu (1 350 m), approx 7 hrs." },
    ]

    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Sarah Johnson",
            role: "Product Manager",
            company: "TechCorp",
            rating: 5,
            content:
                "This product has completely transformed how our team collaborates. The interface is intuitive and the features are exactly what we needed.",
            avatar: "/placeholder.svg?height=60&width=60",
        },
        {
            id: 2,
            name: "Michael Chen",
            role: "Software Engineer",
            company: "StartupXYZ",
            rating: 5,
            content:
                "Outstanding performance and reliability. We've been using this for 6 months now and it's been rock solid. Highly recommend!",
            avatar: "/placeholder.svg?height=60&width=60",
        },
        {
            id: 3,
            name: "Emily Rodriguez",
            role: "Design Lead",
            company: "Creative Studio",
            rating: 4,
            content:
                "The design is beautiful and user-friendly. It's made our workflow so much more efficient. The customer support is also top-notch.",
            avatar: "/placeholder.svg?height=60&width=60",
        },
        {
            id: 4,
            name: "David Thompson",
            role: "Marketing Director",
            company: "Growth Co",
            rating: 5,
            content:
                "Incredible value for money. The analytics features have given us insights we never had before. Game changer for our business.",
            avatar: "/placeholder.svg?height=60&width=60",
        },
        {
            id: 5,
            name: "Lisa Wang",
            role: "CEO",
            company: "Innovation Labs",
            rating: 5,
            content:
                "This solution has scaled perfectly with our growing team. The integration capabilities are fantastic and save us hours every week.",
            avatar: "/placeholder.svg?height=60&width=60",
        },
    ]

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/annapurna-circuit/hero.jpeg"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Annapurna Circuit Fastpacking"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Annapurna Circuit Fastpacking is one of the best long treks on the Earth, that takes hikers around the entire Annapurna Mountain Range over 14 days. The Annapurna circuit trail starts from 800 meters up to 5416 meters in elevation passing through diverse landscapes like forests, fields, rocky ridges, and exotic destinations.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Langtang Valley Fastpacking – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        {/* <TripDateSelector />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}
                        {/* <GroupSizeDiscounts />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}
                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Langtang Valley Fastpacking Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <PackageInformation sections={sections} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default AnnapurnaCircuitFastpackingPage