'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedKoriTrekItinerary } from '@/data/trekking/kori-trek/detailes-itinerary'
import { koriTrekSection } from '@/data/trekking/kori-trek/package-info'

const KoriTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Sikles - Pokhara",
        accommodation: "Tea Houses",
        duration: "Approx 6-7 hrs daily/Can vary",
        maxElevation: "Kori Danda (3,800 m)",
        group: "2–10 max",
        region: "Annapurna Region",
        type: "Unpaved and muddy",
        bestSeason: "March–May & September–November",
        grade: "Moderate",
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Amazing view of mountains from Kori Danda (3,800 m)" },
        { text: "Explore Sikles village's rich culture, museum and try their traditional dress" },
        { text: "Trek through dense lush forests with the sound of birds chirping in your ear" },
        { text: "Panoramic view of Annapurna, Lamjung Himal, Manaslu and more" },
        { text: "Rhododendron blooms and a variety of wildflowers during the season" },
    ];

    const content = {
        paragraphs: [
            `Kori Trek is a new and less-crowded trekking route in Annapurna Region of Nepal. The trekking starts from  a traditional Gurung village. Sikles is a village on the outskirts of Pokhara very famous for their homestays, cultures etc. Kori Trek is one of the best short treks from pokhara. The trail takes you through lush forests, alpine meadows offering stunning views of Annapurna II, Lamjung Himal, and Machhapuchhre (Fishtail). This trek is ideal for anyone looking to escape the hectic schedule of life and immerse themselves in the peace and beauty of nature for a few days.
        `,
        ],
    };

    const note = {
        paragraphs: [
            `Kori Trek is relatively short and can be completed in just a few days. However, it involves a steep uphill climb that may be challenging for some. If you're comfortable with steep ascents, you should be able to complete the trek without much difficulty.`,
            `During the monsoon season, the trail can become very muddy, and leeches are common. Therefore, wearing high boots is strongly recommended.`,
        ],
    };

    const briefing = {
        paragraphs: [
            `Before your departure, we will organize a short online or in-person briefing session where you’ll meet your trek leader and get insights into the itinerary, gear checklist, altitude tips, and cultural dos and don’ts. This ensures a well-informed and safe trekking experience.`,
        ],
    };



    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.",
                "Porter for each 2 person",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.",
                "Fresh/Dry fruits along the trek.",
                "Water bottle to store hot water overnight.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All the necessary paperwork and Annapurna conservation entry permit (ACAP permit & TIMS card etc.)",
            ],
        },
        {
            title: "Medical & Safety",
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                "First Aid Medical Kit Box with an oximeter.",
            ],
        },
        {
            title: "Camping & Equipment",
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                "Tents, cooking gear, and basic camping setup for remote sections of the trail.",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Trek achievement certificate after the trek.",
                "Government taxes, TDS, VAT & other legal documents.",
            ],
        },
    ];

    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipment (sleeping bag, trekking shoes, headlamp, crampons, etc.)",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Porter",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];



    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,324 m)" },
        { day: 2, title: "Kathmandu → Pokhara (822 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 3, title: "Pokhara → Sikles Drive → Trek to Tasa (Approx. 2,600 m) [↑ 1,778 m, ~4 hrs drive + 5 hrs trek]" },
        { day: 4, title: "Tasa → Kori Danda (3,800 m) [↑ 1,200 m, ~6–7 hrs trek]" },
        { day: 5, title: "Kori → Sikles [↓ 1,820 m, ~7 hrs trek]" },
        { day: 6, title: "Sikles → Pokhara (822 m) [↓ 1,158 m, ~4 hrs drive]" },
        { day: 7, title: "Pokhara → Kathmandu (1,324 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 8, title: "Final Departure from Kathmandu" },
    ];

    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Anuja Shrestha",
            role: "Nature Lover",
            company: "Solo Trekker",
            rating: 5,
            content:
                "Kori Trek was an amazing escape from the city. The trail was peaceful, and the view from Kori Danda is something I’ll never forget. Highly recommend this to anyone looking for a short adventure with big rewards!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Daniel Kim",
            role: "Photographer",
            company: "Travel Frame Co.",
            rating: 5,
            content:
                "Perfect mix of culture and landscape. Sikles village was so warm and welcoming, and the rhododendron forest was a dream for photography. Kori is a hidden gem!",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Srijan Lama",
            role: "Adventure Enthusiast",
            company: "HikeMore Nepal",
            rating: 4,
            content:
                "Trail is steep at times but totally worth it. We camped at Tasa and trekked to Kori — the scenery was unreal. Basic tea houses but comfortable enough.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Maya Joshi",
            role: "Travel Blogger",
            company: "Explore With MJ",
            rating: 5,
            content:
                "The silence, the snow-capped peaks, and the sunrise from Kori Danda made this one of the best short treks I’ve done in Nepal. I loved every bit of it!",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Pasang Sherpa",
            role: "Trekking Guide",
            company: "Sherpa Trails",
            rating: 5,
            content:
                "I’ve guided many treks, but Kori is special. Less crowded, beautiful forest trails, and rich Gurung culture. I recommend it to anyone seeking a quieter trek near Pokhara.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Kori Danda Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Kori Trek is a newly emerging and peaceful trail in the Annapurna Region that takes you through lush forests, alpine meadows, and traditional Gurung villages. Starting from the beautiful village of Sikles, the trail offers stunning views of Annapurna II, Lamjung Himal, Machhapuchhre, and even Manaslu on clear days. It is perfect for trekkers looking to experience authentic culture and mountain beauty away from the crowds.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Kori Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedKoriTrekItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/map/trekking/kori-trek.webp"
                            altText="Kori Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={koriTrekSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default KoriTrek
