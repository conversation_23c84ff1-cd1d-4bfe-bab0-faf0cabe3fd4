'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, Car, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedMansaluTrailRunningItinerary } from '@/data/trailrunning/mansalu/itineraryData'
import { mansaluSection } from '@/data/trailrunning/mansalu/package-info'

const MansaluCircuitTrailRunningPage = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Machha Khola - Dharapani',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 6-7 hrs daily/Can vary',
        maxElevation: '(5106 m Larke la pass)',
        group: '8',
        region: 'Annapurna Region',
        mealsIncluded: '(Breakfast, Lunch, and Dinner) during the trek',
        bestSeason: 'March to May and September to November',
        grade: 'Moderate - Difficult (Contains steep uphill and downhill)'
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Trail running in a less crowded yet one of the most beautiful trails of Nepal.' },
        { text: 'Stunning Landscapes, Lush forests, and views of mighty peaks along the way.' },
        { text: 'Conquer the Larke la pass 5106 m and observe the mountains up close.' },
        { text: 'Witness the breathtaking Annapurna and Manaslu mountain ranges.' },
        { text: 'Various Wildlife encounters in the Manaslu Conservation Area.' },
        { text: 'Explore the traditional village with their unique lifestyle.' },
    ]

    const content = {
        paragraphs: [
            `The Manaslu Circuit Trail is one of Nepal’s best trekking routes, offering a less crowded and natural experience. Trail running here takes you around the world’s eighth-highest mountain, passing through serene villages and crossing challenging high mountain passes. Runners will cover longer distances at a faster pace while enjoying the beautiful views and landscape along the way.

            The trail running program starts from Machha Khola and ends at Dharapani exploring various traditional villages in these areas along the way like Jagat, Namrung, Samagaun etc. You will learn the rich culture and traditions of the people living here making this journey more exciting.

             The Manaslu Circuit Trek offers an incredible adventure, combining natural beauty with rich cultural experiences in the Manaslu region of Nepal. Along the way, trekkers you will have awe-inspiring views of towering mountains, valleys, lush forests, and vibrant landscapes. This trail running program will test your utmost physical strength. The program is considered moderate, but it can become challenging due to the difficulty of crossing high altitudes of over 5,000 meters.
                `,
        ],
    }

    const note = {
        paragraphs: [
            `The Manaslu Circuit Trail begins with an 8–9 hour drive from Kathmandu to Machha Khola. Arrive in Kathmandu at least a day before to prepare.`,
            `This trail running involves challenging terrains above 5000 meters, suitable for those experienced in high-altitude treks.`,
        ],
    }

    const briefing = {
        paragraphs: [
            `After confirming your booking, we'll hold an online briefing to explain itinerary details, safety measures, and necessary preparations.`,
        ],
    }


    const myInclusions: Category[] = [
        {
            title: 'Transportation',
            icon: <Car className="w-5 h-5 text-dark" />,
            items: ['Water bottle to store hot water overnight.'], // Closest category fit for now
        },
        {
            title: 'Accommodations',
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                'We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
            ],
        },
        {
            title: 'Guides & Porters',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: ['Porter for each 2 person'],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All the necessary paperwork, Manaslu Conservation Area Project permit, and Annapurna conservation entry permit ( ACAP permit, MCAP permit & TIMS card etc.)',
            ],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with an oximeter.',
                'Trek achievement certificate after the trek.',
                'Government taxes, TDS, VAT & other legal documents.',
            ],
        },
    ];

    const myExclusions: Category[] = [
        {
            title: 'Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any other food/drink should be covered by you, except (3 meals) provided by the company.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Personal equipments'],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: ['Tips for the Guide and Potter'],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: ['Any expenses that are not in the included section.'],
        },
    ];

    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Drive Kathmandu → Machha Khola (870 m), approx 8–9 hrs [↓ 454 m]" },
        { day: 2, title: "Trek Machha Khola → Jagat (1 410 m), 17 km, approx 4–5 hrs [↑ 540 m]" },
        { day: 3, title: "Trek Jagat → Deng (1 800 m), 18.5 km, approx 5–6 hrs [↑ 390 m]" },
        { day: 4, title: "Trek Deng → Namrung (2 660 m), 16 km, approx 4–5 hrs [↑ 860 m]" },
        { day: 5, title: "Trek Namrung → Samagaon (3 530 m), 17.5 km, approx 5 hrs [↑ 870 m]" },
        { day: 6, title: "Trek Samagaon → Dharamsala (4 470 m), 14 km, approx 4–5 hrs [↑ 940 m]" },
        { day: 7, title: "Cross Larke Pass (5 106 m) → Bhimtang (3 720 m), 15.5 km, approx 6–7 hrs [↑ 636 m / ↓ 750 m]" },
        { day: 8, title: "Trek Bhimtang → Dharapani (1 860 m), 22 km, approx 6 hrs [↓ 1 860 m]" },
        { day: 9, title: "Drive Dharapani → Kathmandu (1 324 m), approx 9–10 hrs [↓ 536 m]" },
    ];
    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Amit Kharel",
            role: "Endurance Coach",
            company: "Nepal Mountain Athletics",
            rating: 5,
            content:
                "The Manaslu trail is a hidden gem. The terrain, the altitude, and the peaceful routes offer the perfect blend of challenge and serenity for seasoned runners. Logistics were handled brilliantly!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Jenny Park",
            role: "Ultra Runner",
            company: "Seoul Trail Club",
            rating: 5,
            content:
                "Absolutely breathtaking! Running through remote Himalayan villages with stunning backdrops was surreal. The guides were knowledgeable and the support team made everything smooth.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Carlos Mendes",
            role: "Adventure Blogger",
            company: "TrailNomads",
            rating: 4,
            content:
                "Challenging yet rewarding! The Larke Pass push was intense but totally worth it. A bit more communication pre-trip would’ve helped, but the overall experience was fantastic.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Sita Sharma",
            role: "Fitness Enthusiast",
            company: "HikeNepal",
            rating: 5,
            content:
                "The physical prep paid off! I’ve never felt more alive than I did crossing 5,000+ meters under my own power. Highly recommend this run for anyone ready to test their limits.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Leo Martin",
            role: "Expedition Planner",
            company: "WildStride",
            rating: 5,
            content:
                "Perfectly organized from start to finish. The scenery, the support crew, the cultural insights—everything combined into a life-changing mountain experience.",
            avatar: "/images/review/5.png",
        },
    ]

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image1.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Authentic Adventure: Trail Running the Manaslu Circuit"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is moderate level with decent uphill climb and has to cover long distances during the program. As you will cross the altitude of over 5000+ meters it is important that you are physically fit for this trail running program. This program is recommended for the person who has certain trekking/running experience over 4000+ m. The accommodations in this area are decent - remote with increase in altitude. 
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Mansalu Circuit – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedMansaluTrailRunningItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Mansalu Circuit Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining /> */}
                        {/* <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={mansaluSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default MansaluCircuitTrailRunningPage