import type { Metadata } from 'next';
import { Poppins, Rubik } from 'next/font/google';
import './globals.css';
import Navbar from '@/components/navbar/navbar';
import Footer from '@/components/footer/footer';
import WhatsAppFloat from '@/components/button/whatsapp-floating';

const rubik = Rubik({
  subsets: ['latin'],
  variable: '--font-rubik',
});

const poppins = Poppins({
  variable: '--font-poppins',
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
});

export const metadata: Metadata = {
  title: 'Trail Running & Trekking Adventures',
  description:
    " Discover the world's most majestic mountains through unforgettable guided trail running and trekking experiences.",
};

export const revalidate = 0;

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.variable} ${rubik.variable}  antialiased`}>
        <Navbar />
        {children}
        <WhatsAppFloat />
        <Footer />
      </body>
    </html>
  );
}
