'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import Gallery<PERSON>ithMore from '@/modules/fastpacking/components/video-photo-section'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter, useParams } from 'next/navigation'
import React from 'react'
import { Ban, Car, Clock, FileText, Mountain, Navigation, ShieldCheck } from 'lucide-react'
import { ScrollSpyTabs } from '@/components/common/sticky-scroll/sticky-scroll'
import { usePackageDetail } from '@/modules/package/queries/get-package-detail'

const TrekDetailPage = () => {
    const params = useParams()
    const router = useRouter()
    const slug = params.slug as string

    const { packageDetail, loading, error } = usePackageDetail(slug)

    const handleInquireNow = () => {
        router.push('/contact')
    }

    const handleDatesPrice = () => {
        router.push('/dates-price')
    }

    const handleBookNow = () => {
        if (packageDetail?.bookingLink) {
            window.open(packageDetail.bookingLink, '_blank')
        } else {
            router.push('/booking')
        }
    }

    const sectionIds = [
        { id: "trek-info", label: "Trek Info" },
        { id: "highlights", label: "Highlights" },
        { id: "overview", label: "Overview" },
        { id: "gallery", label: "Gallery" },
        { id: "itinerary", label: "Itinerary" },
        { id: "map", label: "Map" },
        { id: "inclusions", label: "Inclusions/Exclusions" },
        { id: "gears", label: "Gears" },
        { id: "package-info", label: "Package Information" },
        { id: "reviews", label: "Reviews" },
    ]

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-lg">Loading trek details...</p>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-600 mb-4">
                        <p className="text-lg font-semibold">Error loading trek details</p>
                        <p className="text-sm text-gray-600 mt-2">{error}</p>
                    </div>
                    <button
                        onClick={() => window.location.reload()}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        )
    }

    if (!packageDetail) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <p className="text-lg">Trek not found</p>
                    <button
                        onClick={() => router.push('/trekking')}
                        className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Back to Treks
                    </button>
                </div>
            </div>
        )
    }

    const customTrekData = {
        destination: `${packageDetail.region?.name} Region`,
        accommodation: packageDetail.accomodation,
        duration: packageDetail.duration,
        maxElevation: `${packageDetail.altitude}m`,
        group: packageDetail.groupSize,
        region: packageDetail.region?.name,
        type: packageDetail.type,
        bestSeason: packageDetail.bestSeason,
        grade: packageDetail.grade
    }

    const heroImage = packageDetail.mainImage || packageDetail.thumbnail
    const heroTitle = packageDetail.name

    const highlights: HighlightItem[] = packageDetail.highlights?.description?.trim()
        ? packageDetail.highlights.description.split('\n').filter(line => line.trim()).map(text => ({ text: text.trim() }))
        : []

    const hasDescriptionContent = packageDetail.description?.description?.trim()
    const content = {
        paragraphs: hasDescriptionContent 
            ? [packageDetail.description.description.replace(/<[^>]*>/g, '').trim()]
            : []
    }

    const shortItinerary: DaySummary[] = packageDetail.shortItinerary?.points?.length
        ? packageDetail.shortItinerary.points.map((point, index) => ({
            day: index + 1,
            title: point
        }))
        : []

    const galleryItems = packageDetail.gallery?.PackageGalleryImage?.length
        ? packageDetail.gallery.PackageGalleryImage.map((item, index) => ({
            src: item.image,
            alt: item.alt || `${packageDetail.name} - Image ${index + 1}`
        }))
        : []

    const detailedItinerary = packageDetail.itinerary?.length
        ? packageDetail.itinerary
            .sort((a, b) => a.day - b.day)
            .map((item, index, array) => {
                const stats = []
                if (item.trekDistance) {
                    stats.push({
                        icon: <Navigation size={16} className="text-dark/80" />,
                        label: "Distance",
                        value: item.trekDistance
                    })
                }
                if (item.trekDuration) {
                    stats.push({
                        icon: <Clock size={16} className="text-dark/80" />,
                        label: "Duration",
                        value: item.trekDuration
                    })
                }
                if (item.drivingHours) {
                    stats.push({
                        icon: <Car size={16} className="text-dark/80" />,
                        label: "Drive Duration",
                        value: `${item.drivingHours} hrs`
                    })
                }
                if (item.highestAltitude) {
                    stats.push({
                        icon: <Mountain size={16} className="text-dark/80" />,
                        label: "Altitude",
                        value: `${item.highestAltitude}m`
                    })
                }

                return {
                    day: item.day,
                    title: item.title,
                    stats: stats,
                    progressPct: Math.round(((index + 1) / array.length) * 100),
                    description: item.heading,
                    altitudes: item.highestAltitude ? [{
                        label: item.title,
                        value: `${item.highestAltitude}m`
                    }] : undefined
                }
            })
        : []

    const apiReviews: Review[] = packageDetail.review?.length
        ? packageDetail.review
            .filter(review => review.published && review.rating && review.comment)
            .map((review, index) => ({
                id: index + 1,
                name: review.name,
                role: "Trekker",
                company: "Adventure Enthusiast",
                rating: review.rating,
                content: review.comment,
                avatar: review.reviewImage,
            }))
        : []

    const apiVideos: ReviewVideo[] = packageDetail.ytVideo?.links?.length
        ? packageDetail.ytVideo.links.map((link, index) => {
            const youtubeId = link.includes('youtube.com/watch?v=')
                ? link.split('v=')[1]?.split('&')[0]
                : link.includes('youtu.be/')
                    ? link.split('youtu.be/')[1]?.split('?')[0]
                    : link
            return {
                id: `${index + 1}`,
                title: packageDetail.ytVideo.title || `${packageDetail.name} - Video ${index + 1}`,
                youtubeId: youtubeId
            }
        })
        : []

    const apiInclusions: Category[] = packageDetail.inclusions?.details?.trim()
        ? [
            {
                title: packageDetail.inclusions.title || "What's Included",
                icon: <ShieldCheck className="w-5 h-5 text-dark" />,
                items: packageDetail.inclusions.details
                    .split('\n')
                    .filter(item => item.trim())
                    .map(item => item.trim())
            }
        ]
        : []

    const apiExclusions: Category[] = packageDetail.exclusions?.details?.trim()
        ? [
            {
                title: packageDetail.exclusions.title || "What's Not Included",
                icon: <Ban className="w-5 h-5 text-dark" />,
                items: packageDetail.exclusions.details
                    .split('\n')
                    .filter(item => item.trim())
                    .map(item => item.trim())
            }
        ]
        : []

    return (
        <>
            {heroImage && (
                <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                    <HeroSection
                        imageSrc={heroImage}
                        imageAlt={packageDetail.mainImageAlt || `${heroTitle} - ${packageDetail.type} in ${packageDetail.region?.name}`}
                        title={heroTitle}
                    />
                </div>
            )}

            <div className="container mx-auto px-2 md:px-4 py-8">
                <ScrollSpyTabs sectionIds={sectionIds} />

                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration={packageDetail.duration}
                                originalPrice={packageDetail.discountPrice ? `USD ${packageDetail.price}` : undefined}
                                currentPrice={`USD ${packageDetail.discountPrice || packageDetail.price} pp`}
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />

                            {packageDetail.groupPrice?.filter(gp => gp.published).length > 0 && (
                                <div className="bg-white p-4 rounded-lg border">
                                    <h3 className="font-semibold mb-3">Group Pricing</h3>
                                    <div className="space-y-2">
                                        {packageDetail.groupPrice
                                            .filter(gp => gp.published)
                                            .map((gp) => (
                                                <div key={gp.id} className="flex justify-between text-sm">
                                                    <span>{gp.numberOfPeople} people:</span>
                                                    <span className="font-medium">{gp.pricePerPerson}</span>
                                                </div>
                                            ))
                                        }
                                    </div>
                                </div>
                            )}

                            {packageDetail.pdfBrochure && (
                                <div className="bg-white p-4 rounded-lg border">
                                    <h3 className="font-semibold mb-3">Download Brochure</h3>
                                    <a
                                        href={packageDetail.pdfBrochure}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm"
                                    >
                                        <FileText className="w-4 h-4" />
                                        Download PDF Brochure
                                    </a>
                                </div>
                            )}
                        </div>
                    </div>

                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <section id="trek-info" className="scroll-mt-[200px]">
                            <TrekInfo trekData={customTrekData} />
                            <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                            {packageDetail.overviewDescription?.trim() && (
                                <div className="prose prose-lg max-w-none">
                                    <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                        <div
                                            className="text-justify"
                                            dangerouslySetInnerHTML={{
                                                __html: packageDetail.overviewDescription
                                            }}
                                        />
                                    </div>
                                </div>
                            )}
                        </section>

                        {highlights.length > 0 && (
                            <section id="highlights" className="scroll-mt-[200px]">
                                <Highlight items={highlights} />
                                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            </section>
                        )}

                        {hasDescriptionContent && (
                            <section id="overview" className="scroll-mt-[200px]">
                                <Overview
                                    content={{
                                        paragraphs: [packageDetail.description.description.replace(/<[^>]*>/g, '').trim()]
                                    }}
                                    note={{
                                        paragraphs: [
                                            `This ${packageDetail.type.toLowerCase()} has a ${packageDetail.grade.toLowerCase()} difficulty rating. Proper preparation and fitness are essential.`,
                                            `Best season for this adventure is ${packageDetail.bestSeason}. Weather conditions can change rapidly in the mountains.`
                                        ]
                                    }}
                                    briefing={{
                                        paragraphs: [
                                            `We'll conduct a pre-trip briefing to discuss the ${packageDetail.name} route, safety guidelines, equipment requirements, and what to expect during your ${packageDetail.duration} adventure.`
                                        ]
                                    }}
                                />
                                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            </section>
                        )}

                        {shortItinerary.length > 0 && (
                            <section className="scroll-mt-[200px]">
                                <ItinerarySummary
                                    title={packageDetail.shortItinerary?.title || `${packageDetail.name} – Short Itinerary`}
                                    days={shortItinerary}
                                />
                                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            </section>
                        )}
                        
                        {galleryItems.length > 0 && (
                            <section id="gallery" className="scroll-mt-[200px]">
                                <GalleryWithMore items={galleryItems} />
                                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            </section>
                        )}

                        {detailedItinerary.length > 0 && (
                            <section id="itinerary" className="scroll-mt-[200px]">
                                <ItineraryDetailed days={detailedItinerary} />
                                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            </section>
                        )}

                        <section id="map" className="scroll-mt-[200px]">
                            <Mapandchart
                                imageSrc={packageDetail.map?.map || `/images/map/trekking/${packageDetail.slug}-map.webp`}
                                altText={`${packageDetail.name} Route Map`}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        {(apiInclusions.length > 0 || apiExclusions.length > 0) && (
                            <section id="inclusions" className="scroll-mt-[200px]">
                                <TrekInclusionsExclusions
                                    inclusions={apiInclusions}
                                    exclusions={apiExclusions}
                                />
                                <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            </section>
                        )}

                        <section id="gears" className="scroll-mt-[200px]">
                            {packageDetail.equipment?.length > 0 ? (
                                <div className="bg-white p-6 rounded-lg">
                                    <h2 className="text-2xl font-bold mb-6">Equipment & Gear</h2>
                                    <div className="space-y-6">
                                        {packageDetail.equipment.map((equipment) => (
                                            <div key={equipment.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                                                <h3 className="font-semibold text-lg mb-2">{equipment.title}</h3>
                                                <p className="text-gray-600 mb-3">{equipment.description}</p>
                                                <div className="grid md:grid-cols-3 gap-4 text-sm">
                                                    {equipment.head && (
                                                        <div>
                                                            <h4 className="font-medium mb-1">Head Gear</h4>
                                                            <div dangerouslySetInnerHTML={{ __html: equipment.head }} />
                                                        </div>
                                                    )}
                                                    {equipment.body && (
                                                        <div>
                                                            <h4 className="font-medium mb-1">Body Gear</h4>
                                                            <div dangerouslySetInnerHTML={{ __html: equipment.body }} />
                                                        </div>
                                                    )}
                                                    {equipment.face && (
                                                        <div>
                                                            <h4 className="font-medium mb-1">Face Protection</h4>
                                                            <div dangerouslySetInnerHTML={{ __html: equipment.face }} />
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            ) : (
                                <Gears />
                            )}
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="package-info" className="scroll-mt-[200px]">
                            <div className="bg-white p-6 rounded-lg">
                                <h2 className="text-2xl font-bold mb-4">Package Information</h2>
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-semibold mb-2">{packageDetail.type} Details</h3>
                                        <ul className="space-y-1 text-sm">
                                            <li><strong>Duration:</strong> {packageDetail.duration}</li>
                                            <li><strong>Max Altitude:</strong> {packageDetail.altitude}m</li>
                                            <li><strong>Grade:</strong> {packageDetail.grade}</li>
                                            <li><strong>Group Size:</strong> {packageDetail.groupSize}</li>
                                            <li><strong>Activity:</strong> {packageDetail.activity.name}</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h3 className="font-semibold mb-2">Logistics</h3>
                                        <ul className="space-y-1 text-sm">
                                            <li><strong>Transport:</strong> {packageDetail.transport}</li>
                                            <li><strong>Accommodation:</strong> {packageDetail.accomodation}</li>
                                            <li><strong>Meals:</strong> {packageDetail.meals}</li>
                                            <li><strong>Activities:</strong> {packageDetail.activityPerDay}</li>
                                        </ul>
                                    </div>
                                </div>

                                {packageDetail.info?.items?.length > 0 && (
                                    <div className="mt-6">
                                        <h3 className="font-semibold mb-3">{packageDetail.info.title || "Additional Information"}</h3>
                                        <div className="space-y-2">
                                            {packageDetail.info.items.map((item, index) => (
                                                <div key={index} className="text-sm text-gray-600">
                                                    {item.title}
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}

                                {packageDetail.costDate?.filter(cd => cd.published).length > 0 && (
                                    <div className="mt-6">
                                        <h3 className="font-semibold mb-3">Available Dates & Pricing</h3>
                                        <div className="grid gap-3">
                                            {packageDetail.costDate
                                                .filter(cd => cd.published)
                                                .map((costDate) => (
                                                    <div key={costDate.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                                        <div>
                                                            <p className="font-medium">{new Date(costDate.startDate).toLocaleDateString()} - {new Date(costDate.endDate).toLocaleDateString()}</p>
                                                            <p className="text-sm text-gray-600">{costDate.days} days • {costDate.tripStatus}</p>
                                                        </div>
                                                        <div className="text-right">
                                                            <p className="font-semibold">{costDate.discountPrice}</p>
                                                            {costDate.price !== costDate.discountPrice && (
                                                                <p className="text-sm text-gray-500 line-through">{costDate.price}</p>
                                                            )}
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                    </div>
                                )}
                            </div>
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        {(apiVideos.length > 0 || apiReviews.length > 0) && (
                            <section id="reviews" className="scroll-mt-[200px]">
                                {apiVideos.length > 0 && (
                                    <>
                                        <TravelersReview
                                            videos={apiVideos}
                                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                                        />
                                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                                    </>
                                )}

                                {apiReviews.length > 0 && (
                                    <StackedReviews
                                        reviews={apiReviews}
                                        headerTitle="Customer Reviews"
                                    />
                                )}
                            </section>
                        )}
                    </div>
                </div>
            </div>

            {packageDetail.faq?.filter(faq => faq.published)?.length > 0 && (
                <div className="container mx-auto px-2 md:px-4 pb-8">
                    <div className="bg-white p-6 rounded-lg">
                        <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>
                        <div className="space-y-4">
                            {packageDetail.faq
                                .filter(faq => faq.published)
                                .map((faq) => (
                                    <div key={faq.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                                        <h3 className="font-semibold text-lg mb-2">{faq.question}</h3>
                                        <div
                                            className="text-gray-600"
                                            dangerouslySetInnerHTML={{ __html: faq.answer }}
                                        />
                                    </div>
                                ))
                            }
                        </div>
                    </div>
                </div>
            )}

            <FAQSection />
        </>
    )
}

export default TrekDetailPage
