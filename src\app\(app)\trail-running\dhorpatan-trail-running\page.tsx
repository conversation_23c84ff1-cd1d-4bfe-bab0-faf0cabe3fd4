'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, Car, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedDhorpatanItinerary } from '@/data/trailrunning/dhorpatan/itinerary-detailes'
import { dhorpatanSection } from '@/data/trailrunning/dhorpatan/package-info'

const DhorpatanTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Dhorpatan Valley - Takam Village',
        accommodation: 'Remote villages with small tea houses',
        duration: 'Approx 6-7 hrs daily',
        maxElevation: '(2,850 to 5,500 m Dhorpatan Hunting Reserve)',
        group: 'Varies',
        region: 'Western Nepal (Rukum, Myagdi, Baglung)',
        type: 'Mixture of Unpaved and muddy paths',
        bestSeason: 'March to May and September to November',
        grade: 'Moderate - Difficult (Contains steep uphill and downhill)'
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Explore the only hunting reserve of Nepal.' },
        { text: 'Witness Jaljala Pass—one of the region’s high points.' },
        { text: 'Scenic mountain views of Annapurna and Dhaulagiri during the run.' },
        { text: 'Magar villages and people with welcoming hospitality.' },
        { text: 'Wildlife, natural resources, flowers, and vegetation are the main attractions of Dhorpatan.' },
        { text: 'Remote trail with very few people—ideal for runners seeking non-crowded yet exciting routes.' },
    ];


    const content = {
        paragraphs: [
            `Dhorpatan is one of Nepal’s hidden gems, located in the western part of the country across the districts of Rukum, Myagdi, and Baglung. It is a remote, peaceful region known for its dense forests, and beautiful mountain views. The area sits at a high elevation, with green valleys and rolling hills surrounded by snowy peaks, making it feel untouched and wild. Because it is less visited, Dhorpatan offers a true off-the-beaten-path experience for nature lovers and adventure seekers.

            One of the main highlights of this region is the Dhorpatan Hunting Reserve. It is the only hunting reserve in Nepal, covering an area of about 1,325 square kilometers. The reserve was established to allow hunting of certain animals like blue sheep and Himalayan tahr, while also protecting many endangered species like the red panda and snow leopard. Besides wildlife, the reserve is rich in different types of plants and forests, including rhododendron, pine, and oak.

            For trail runners, Dhorpatan is a dream location. The open meadows, forest trails, and rugged mountain paths offer endless opportunities for running. The routes are challenging yet scenic, with quiet villages and beautiful river valleys along the way. It’s perfect for those looking for wild, remote trail running adventures away from the crowds.`,
        ],
    };

    const note = {
        paragraphs: [
            `This trail covers rugged and remote terrain in western Nepal, including high mountain passes and forested valleys. Be prepared for limited facilities and no mobile reception in some stretches.`,
            `Due to the remote nature of the Dhorpatan region, participants should arrive in Pokhara at least a day before departure for gear checks and final coordination.`,
        ],
    };

    const briefing = {
        paragraphs: [
            `Once your registration is confirmed, we will conduct an online or in-person briefing covering safety protocols, trail route, gear list, and contingency plans. This is mandatory for all participants.`,
        ],
    };


    const myInclusions: Category[] = [
        {
            title: 'Transportation',
            icon: <Car className="w-5 h-5 text-dark" />,
            items: [
                'All ground transportation during the trip including drive from Pokhara to Dhorpatan and back from Takam to Pokhara by Jeep/Bus.',
            ],
        },
        {
            title: 'Accommodations',
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                'Basic accommodation in remote tea houses and lodges during the trail.',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                'We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
            ],
        },
        {
            title: 'Guides & Porters',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.',
                'Porter for each 2 person.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All necessary paperwork and Annapurna Conservation Area Entry Permit (ACAP permit & TIMS card etc.)',
            ],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with an oximeter.',
                'Trek achievement certificate after the trek.',
                'Water bottle to store hot water overnight.',
                'Government taxes, TDS, VAT & other legal documents.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any food/drinks not provided in the included 3 meals.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                'Personal equipment such as running shoes, clothes, water purifiers, etc.',
            ],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                'Tips for the guide and porter.',
            ],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Additional costs due to unforeseen circumstances (illness, bad weather, natural calamities, etc.).',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                'Any other expenses not mentioned in the included section.',
            ],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Drive Pokhara → Dhorpatan via Burtibang (2,900 m), approx 9–10 hrs [↑ 2,078 m]" },
        { day: 2, title: "Explore outskirts of Dhorpatan Valley (3,000–3,100 m), 8–10 km, approx 3–4 hrs [↑ 200 m]" },
        { day: 3, title: "Run Dhorpatan → Jaljala Pass via Gurjaghat and return (3,000 m), 29 km, approx 7–8 hrs [↑ 400 m]" },
        { day: 4, title: "Run Gurjaghat → Gurja Khani (2,650 m), 17.5 km, approx 6–7 hrs [↓ 350 m]" },
        { day: 5, title: "Run Gurja Khani → Takam Village (1,650 m), 25.5 km, approx 7 hrs [↓ 1,000 m]" },
        { day: 6, title: "Drive Takam → Pokhara (822 m), approx 5–6 hrs [↓ 828 m]" },
    ];

    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Nirmal Thapa",
            role: "Mountain Runner",
            company: "FastTrails Nepal",
            rating: 5,
            content:
                "The Dhorpatan region completely blew my mind! Running through such wild and remote landscapes was both calming and intense. Jaljala Pass was a real challenge, but the views made it all worth it.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Sophie Lin",
            role: "Nature Explorer",
            company: "EcoTreks Taiwan",
            rating: 5,
            content:
                "If you're looking for silence and beauty, Dhorpatan is it. Zero crowds, pristine forests, and the wildlife! The red panda sighting was a dream come true. A must-do trail for offbeat adventure lovers.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Rahul Sen",
            role: "Trail Blogger",
            company: "RunNomad",
            rating: 4,
            content:
                "Epic route! The altitude, rugged terrain, and minimal infrastructure really test your preparation. I'd love better marking or GPX assistance, but the remoteness is part of the magic.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Anisha Gurung",
            role: "Outdoor Coach",
            company: "PeakFit Nepal",
            rating: 5,
            content:
                "Loved how raw and untouched the trail felt. Gurja Khani was a hidden cultural gem. The team organized everything perfectly and our guide really knew the terrain well.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Tom Becker",
            role: "Backcountry Athlete",
            company: "WildEdge Adventures",
            rating: 5,
            content:
                "Hard to believe a place like this exists and isn’t packed with tourists. Dhorpatan is a paradise for serious trail runners. Highly recommend for anyone craving isolation and altitude.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image7.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title=" Discover the Untouched Trails of Dhorpatan: A Remote Himalayan Running Adventure"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is moderate level with decent uphill climb and has to cover long distances during the program. As you will cross the altitude of over 5000+ meters it is important that you are physically fit for this trail running program. This program is recommended for the person who has certain trekking/running experience over 4000+ m. The accommodations in this area are decent - remote with increase in altitude.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Dhorpatan Trail Running – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedDhorpatanItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/dhorpatan/map.webp"
                            altText="Dhorpatan Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
{/* 
                        <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={dhorpatanSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default DhorpatanTrailRunning