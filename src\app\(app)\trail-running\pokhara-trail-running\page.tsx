'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { pokharaTrailRunning } from '@/data/trailrunning/pokhara-trailrunning/pokhara-detailes-itinerary'
import { pokharaTrailSection } from '@/data/trailrunning/pokhara-trailrunning/package-info'

const PokharaTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Hemja – Pokhara',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 6-7 hrs daily/Can vary',
        maxElevation: '(2500 m)',
        group: '6',
        region: 'Annapurna Region',
        type: 'Mixture of Paved and Unpaved paths',
        bestSeason: 'March to May and September to November',
        grade: 'Easy - Moderate',
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Breathtaking view of Mighty Annapurna (8091 m) and Machhepuchhre (6993 m).' },
        { text: 'Culturally Enriched villages around Pokhara valley.' },
        { text: 'Lush Forests with various medical herbs, wildlife, flowers.' },
        { text: 'Paved, Unpaved and Muddy roads possessing exciting challenges to runners.' },
        { text: 'Exciting programs and activities for the runners daily in lodge or tea houses.' },
    ];


    const content = {
        paragraphs: [
            `This 6 Days Trail Running Program carries you through the tough terrains around Pokhara valley with exciting landscapes, breathtaking mountain views, traditional culturally enriched villages etc. The steep uphill will challenge your utmost physical fitness. The exciting views of Annapurna South (7,219 m | 23,684 ft) and Machhapuchhre mountain (6,993 m | 22,943 ft) from the viewpoint while racing gives you a reason to continue this trail further.

    The journey begins from Hemja and ends at Pokhara covering Pitam Deurali, Landruk, Bhadure, Panchase, Jhuma Danda. This 6 days program should be in the bucket list of every trail running enthusiast. The participants will run around 4-5 hours daily covering the distance ranging from 12 - 25 km each day.

    In this trail, the runner will encounter three different types of terrain: Paved Road - It is smooth, hard surface which makes runners cover more distance in less time offering good traction and speed. Muddy Road – It is challenging to run on a muddy surface, Muddy roads are slippery and possess challenges to the runners. Unpaved Road – This is a rougher surface like dirt, gravel, or loose rocks. It’s not as smooth as pavement and can vary in difficulty depending on how uneven or rocky it is.

    The trail runners should be prepared for easy, moderate and difficult roads and terrain during this program.`,
        ],
    };


    const note = {
        paragraphs: [
            `This trail running journey starts from Hemja, Pokhara and ends in Pokhara itself, covering a circular loop across Panchase, Landruk, and Jhuma Danda.`,
            `Although this program is rated easy to moderate, it includes some steep ascents and rugged terrain. Arrive in Pokhara a day before the start to get briefed and ready.`,
        ],
    };


    const briefing = {
        paragraphs: [
            `Once your booking is confirmed, our team will arrange a virtual or in-person pre-run briefing in Pokhara. We'll cover essential safety instructions, daily plans, packing guidelines, and hydration strategy to ensure you're well-prepared.`,
        ],
    };

    const myInclusions: Category[] = [
        {
            title: 'Guides',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                'We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All the necessary paperwork and Annapurna conservation entry permit ( ACAP permit & TIMS card etc.)',
            ],
        },
        {
            title: 'Porters',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Porter for each 2 person'],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with an oximeter.',
                'Trek achievement certificate after the trek.',
                'Water bottle to store hot water overnight.',
                'Government taxes, TDS, VAT & other legal documents.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any other food/drink should be covered by you, except (3 meals) provided by the company.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Personal equipments'],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: ['Tips for the Guide and Potter'],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: ['Any expenses that are not in the included section.'],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Trek Hemja → Pitam Deurali (2 100 m), 15 km, approx 3–4 hrs [↑ 933 m]" },
        { day: 2, title: "Trek Pitam Deurali → Landruk (1 565 m), 11.5 km, approx 3 hrs [↓ 535 m]" },
        { day: 3, title: "Trek Landruk → Bhadaure (1 400 m), 18 km, approx 5 hrs [↓ 165 m]" },
        { day: 4, title: "Trek Bhadaure → Panchase (2 500 m), 8 km, approx 2–2.5 hrs [↑ 1100 m]" },
        { day: 5, title: "Trek Panchase → Jhuma Danda (1 580 m), 10 km, approx 3 hrs [↓ 920 m]" },
        { day: 6, title: "Trek Jhuma Danda → Pokhara (822 m), 11.2 km, approx 3 hrs [↓ 758 m]" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Anita Gurung",
            role: "Trail Runner",
            company: "Pokhara Endurance Club",
            rating: 5,
            content:
                "A stunning experience running through forest, hills, and cultural villages! The mix of paved and muddy paths made every day different. Great support team and the mountain views were surreal.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Daniel Yu",
            role: "Running Coach",
            company: "TrailFit Korea",
            rating: 5,
            content:
                "The Pokhara trail is ideal for both training and cultural immersion. Varied terrain challenged our runners, and the local hospitality at tea houses added a warm touch to the adventure.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Emma Weston",
            role: "Fitness Blogger",
            company: "RunTheWorld",
            rating: 4,
            content:
                "Loved the trail diversity—from scenic ridge runs to peaceful jungle descents. The climb to Panchase was tough but rewarding. Minor delay at one lodge, but everything else was top-notch.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Sudeep Thapa",
            role: "Adventure Runner",
            company: "Himalayan Motion",
            rating: 5,
            content:
                "The blend of technical sections and village runs made it one of my favorite multi-day trail experiences. Big thanks to the crew for keeping everything on track and fun!",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Lara Silva",
            role: "Travel Vlogger",
            company: "FeetOnTrails",
            rating: 5,
            content:
                "Running past waterfalls, rhododendron forests, and temples—it felt like a dream. The panoramic views from Panchase were unforgettable. Definitely a must-do trail!",
            avatar: "/images/review/5.png",
        },
    ];

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image2.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Trail Running in the Heart of the Himalayas: From Kapuche to Kori"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is moderate level with decent uphill climb and has to cover long distances during the program. As you will cross the altitude of over 5000+ meters it is important that you are physically fit for this trail running program. This program is recommended for the person who has certain trekking/running experience over 4000+ m. The accommodations in this area are decent - remote with increase in altitude.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Pokhara Trail Running – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={pokharaTrailRunning} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Pokhara Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={pokharaTrailSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default PokharaTrailRunning