'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedTsumValleyItinerary } from '@/data/tsum-valley-trek/detailed-itinerary'
import { tsumValleyTrek } from '@/data/tsum-valley-trek/package-info'

const TsumValleyTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Kathmandu - Machhakhola (Covers Tsum Valley)",
        accommodation: "Tea Houses",
        duration: "Approx 6–7 hrs daily/Can vary",
        maxElevation: "Mu Gompa (3,700 m)",
        group: "2–10 max",
        region: "Manaslu Region",
        type: "Unpaved and Remote",
        bestSeason: "March–May & September–November",
        grade: "Moderate to Challenging",
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Take you to the border of Tibet, explore the unique Tibetan culture of people living there" },
        { text: "Tsum valley is filled with monasteries and chortens, reflecting their old culture" },
        { text: "It is a sacred valley in the northern region of Manaslu with the magnificent backdrop of Ganesh Himal and Sringi Himal" },
        { text: "See the sacred footprint of Jetsun Milarepa at the Piren Phu Cave" },
        { text: "Remote and hidden area explored by very few trekkers" },
        { text: "Visit Mu Gompa, the biggest monastery in Tsum Valley at an altitude of 3,650 m" },
    ];



    const content = {
        paragraphs: [
            `Tsum valley trek, one of the least explored remote journeys through the hidden valley of Tsum, is located in the Northern part of Manaslu Region. Only opened in 2008, this trail is gaining a bit of popularity among trekkers. It is a sacred valley with the magnificent background of Ganesh Himal and Sringi Himal. Unlike the busy Annapurna region, the Manaslu and Tsum Valley areas remain largely untouched and are visited by very few trekkers. 

Tsum Valley is known for its deep Buddhist roots, the trek takes you through traditional villages, ancient monasteries, and serene landscapes untouched by modern development. You'll walk along trails surrounded by towering mountains, cross suspension bridges, and pass through pine forests. This trek offers a peaceful experience for trekkers seeking cultural immersion, scenic beauty, and fewer crowds.

                    `,
        ],
    };

    const note = {
        paragraphs: [
            `Tsum Valley Trek takes you through remote and high-altitude areas. Although it doesn’t involve extreme elevation gains like other treks, maintaining good physical fitness and proper acclimatization is still important for safety and enjoyment.`,

            `The weather in the Manaslu region can be unpredictable, especially at higher altitudes. Always carry layered clothing, rain protection, and follow your guide’s instructions while navigating narrow trails or river crossings.`,
        ],
    };




    const briefing = {
        paragraphs: [
            `Before your departure, we’ll organize a detailed trip briefing either online or in-person. During this session, you’ll meet your trek leader and go over the itinerary, gear checklist, safety protocols, and altitude-related precautions. This ensures you are fully prepared for the Three Passes adventure.`,
        ],
    };


    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional Guide and Potters, their food, accommodation and insurance etc.",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 Meals per day which includes Breakfast, Lunch and Dinner.",
                "Medicines and water purifiers will be provided.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "Permits and any other necessary documents.",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government and service tax.",
            ],
        },
    ];

    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipments",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Potter",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,324 m)" },
        { day: 2, title: "Drive from Kathmandu/Pokhara to Machhakhola (870 m) [~8–10 hrs, ↓ 454 m]" },
        { day: 3, title: "Trek from MachhaKhola to Jagat (1,300 m) [22 km, ~7 hrs, ↑ 430 m]" },
        { day: 4, title: "Jagat to Lokpa (2,240 m) [16 km, ~7–8 hrs, ↑ 940 m]" },
        { day: 5, title: "Lokpa to Gho Village (2,415 m) [17 km, ~6–7 hrs, ↑ 175 m]" },
        { day: 6, title: "Gho to Nile (3,480 m) [17 km, ~6–7 hrs, ↑ 1,065 m]" },
        { day: 7, title: "Nile to Mu Gompa (3,700 m) and return to Chhekampar (3,000 m) [16 km, ~6–7 hrs, ↓ 480 m]" },
        { day: 8, title: "Chhekampar to Chumling (2,386 m) [15.5 km, ~6–7 hrs, ↓ 614 m]" },
        { day: 9, title: "Chumling to Philim (1,590 m) [13 km, ~5 hrs, ↓ 796 m]" },
        { day: 10, title: "Philim to Machhakhola (870 m) [23 km, ~6–7 hrs, ↓ 720 m]" },
        { day: 11, title: "Drive back from Machhakhola to Kathmandu/Pokhara (1,324 m) [~8–10 hrs, ↑ 454 m]" },
        { day: 12, title: "Final Departure from Kathmandu" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Isabella Thorne",
            role: "Solo Adventurer",
            company: "Wanderlust Diaries",
            rating: 5,
            content:
                "Tsum Valley felt like stepping into another world. The peaceful monasteries, untouched trails, and kind-hearted locals made this trek truly unforgettable. A hidden gem!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Ngawang Sherpa",
            role: "Trek Guide",
            company: "Himalayan Treks",
            rating: 5,
            content:
                "This trail offers more than just mountains. It’s about culture, spirituality, and exploring places where few outsiders have gone. Highly recommended for those seeking raw Himalayan beauty.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Lisa Möller",
            role: "Photographer",
            company: "Frames & Peaks",
            rating: 4,
            content:
                "Incredible cultural experience. Mu Gompa and the views of Ganesh Himal were breathtaking. The trail is remote and tough at times, but worth every step.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Sujan Gurung",
            role: "Trekking Enthusiast",
            company: "Nepal Explorer",
            rating: 5,
            content:
                "The spiritual energy in Tsum Valley is real. The mani walls, chortens, and Milarepa’s cave gave the trek a very special vibe. Loved the solitude and connection to nature.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Marina Lopez",
            role: "Adventure Blogger",
            company: "Trails of Peace",
            rating: 5,
            content:
                "This was my favorite trek in Nepal. It’s peaceful, sacred, and culturally rich. I’ll never forget the sunrise over Mu Gompa and the warmth of the villagers.",
            avatar: "/images/review/5.png",
        },
    ];



    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Tsum Valley Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        Tsum Valley Trek, one of the least explored journeys in Nepal, takes you through the sacred hidden valley located in the northern Manaslu region. Opened only in 2008, this trail remains largely untouched by modern development and offers an authentic Himalayan experience.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Tsum Valley Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedTsumValleyItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/map/trekking/tsum-valley-trek.webp"
                            altText="Tsum Valley Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={tsumValleyTrek} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default TsumValleyTrek
