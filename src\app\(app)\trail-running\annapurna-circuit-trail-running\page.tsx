'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, Car, FileText, HandCoins, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { mansaluSection } from '@/data/trailrunning/mansalu/package-info'
import { detailedAnnapurnaCircuitItinerary } from '@/data/trailrunning/annapurna-circuit/itinerary-detailed'

const AnnapurnaCircuitTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Marpha',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 6-7 hrs daily/Can vary',
        maxElevation: '(5416 m - Thorong la pass)',
        group: '8',
        region: 'Annapurna Region',
        type: 'Mixture of Unpaved and muddy paths',
        bestSeason: 'March to May and September to November',
        grade: 'Moderate - Difficult (Contains steep uphill and downhill)'
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Conquer the Thorung La Pass at 5,416 m — one of the highest mountain passes in the world.' },
        { text: 'Reach Tilicho Lake at 4,919 m — the highest glacial lake on Earth.' },
        { text: 'Enjoy breathtaking views of Annapurna, Dhaulagiri, and Tilicho Peak.' },
        { text: 'Run through culturally rich villages of Gurung, Thakali, and Tibetan-influenced communities.' },
        { text: 'Pass ancient Buddhist monasteries, prayer wheels, and chortens along the trail.' },
        { text: 'Experience rhododendron forests and spot exotic wildlife like Blue Sheep or Snow Leopards.' },
        { text: 'Cover the entire Annapurna Circuit in a compact, fast-paced running itinerary.' },
        { text: 'Tackle technical uphill and downhill trails across diverse terrain from jungle to alpine desert.' },
        { text: 'Get the thrill of high-altitude endurance running across some of Nepal’s most scenic trails.' },

    ]

    const content = {
        paragraphs: [
            `The Annapurna Circuit Trail Running is one of the best long treks in Nepal, that takes runners around the entire Annapurna Mountain Range. The Annapurna circuit trail starts from around 800 meters up to 5416 meters in elevation passing through diverse landscapes like forests, fields, rocky ridges, and exotic destinations.

      The major trail running highlights of Annapurna Circuit include exploring settlements of differing cultures, learning about the ancient traditions of Nubri and Lubra people, visiting famed places like Muktinath, Jomsom, and Poon Hill, and crossing Thorong La Pass – one of the highest mountain passes globally.

      An additional major highlight along the trail itself is Tilicho Lake, at 4919 m — the highest glacial lake in the world. By trekking to Tilicho Lake as part of completing the entire Annapurna Circuit route, runners enjoy both high-altitude challenge and serene remoteness in a compact time frame.

      This program is suitable for experienced runners and trekkers, particularly those who have trained or hiked at elevations above 4000 m. With proper preparation, the blend of stunning landscapes, cultural diversity, and physical challenges offers an unforgettable Himalayan adventure.`
        ]
    }

    const note = {
        paragraphs: [
            `The Annapurna Circuit Trail begins with a long drive from Kathmandu or Pokhara to Dharapani via Besisahar. Arrive at least a day before in Kathmandu or Pokhara to prepare accordingly.`,
            `This trail running route involves steep ascents, high elevation passes over 5000 meters, and technical downhills. Prior experience with high-altitude trekking or mountain running is strongly recommended.`,
        ],
    }

    const briefing = {
        paragraphs: [
            `After confirming your booking, we'll hold an online briefing to explain itinerary details, safety measures, and necessary preparations.`,
        ],
    }


    const myInclusions: Category[] = [
        {
            title: 'Guide & Support',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide.',
                'Guide’s food, accommodation, salary, equipment, and insurance.',
                'Porter service (1 porter per 2 people).',
            ],
        },
        {
            title: 'Meals & Refreshments',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                '3 meals per day (Breakfast, Lunch, Dinner).',
                'Tea or coffee served twice daily.',
                'Fresh and dry fruits during the trek.',
            ],
        },
        {
            title: 'Permits & Documentation',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'Annapurna Conservation Area Permit (ACAP).',
                'TIMS Card.',
                'All required paperwork and government taxes (VAT, TDS, etc.).',
            ],
        },
        {
            title: 'Medical & Safety',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit with oximeter.',
                'Trek completion certificate.',
            ],
        },
        {
            title: 'Transport & Logistics',
            icon: <Car className="w-5 h-5 text-dark" />,
            items: [
                'Transportation to/from trek starting and ending points.',
                'Water bottle to store hot water overnight.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Additional Food & Beverages',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any food or drinks outside of the 3 meals provided per day.',
            ],
        },
        {
            title: 'Personal Gear',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                'Personal running and trekking equipment.',
            ],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                'Tips for guides and porters.',
            ],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Costs due to illness, weather, delays, or other unforeseen events.',
            ],
        },
        {
            title: 'Miscellaneous',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                'Any expenses not specifically listed in the inclusions section.',
            ],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Drive from Kathmandu/Pokhara → Dharapani (1,860 m), approx 8–9 hrs" },
        { day: 2, title: "Run Dharapani → Chame (2,650 m), 16 km, approx 5 hrs [↑ 790 m]" },
        { day: 3, title: "Run Chame → Manang (3,519 m), 33 km, approx 7 hrs [↑ 869 m]" },
        { day: 4, title: "Run Manang → Tilicho Base Camp (4,150 m), 14.5 km, approx 5–6 hrs [↑ 631 m]" },
        { day: 5, title: "Run Tilicho BC → Tilicho Lake (4,919 m) → Yak Kharka (4,020 m), 26 km, 7–8 hrs [↑ 769 m / ↓ 130 m]" },
        { day: 6, title: "Run Yak Kharka → Thorong Phedi (4,450 m), 7.5 km, approx 2–3 hrs [↑ 430 m]" },
        { day: 7, title: "Cross Thorong La Pass (5,416 m) → Muktinath (3,800 m), 14.5 km, approx 5 hrs [↑ 966 m / ↓ 650 m]" },
        { day: 8, title: "Run Muktinath → Marpha (2,650 m), 26 km, approx 6–7 hrs [↓ 1,150 m]" },
        { day: 9, title: "Drive Marpha → Pokhara (822 m), approx 4–5 hrs [↓ 1,828 m]" },
    ];

    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Ravi Gurung",
            role: "Ultra Trail Runner",
            company: "Kathmandu Trail Club",
            rating: 5,
            content:
                "Running the Annapurna Circuit was the most transformative experience of my life. From Tilicho Lake to Thorong La Pass, every step felt epic. Huge shoutout to the guides for keeping us safe and inspired!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Elena Fischer",
            role: "Fitness Coach",
            company: "Run Wild Europe",
            rating: 5,
            content:
                "This was my first time running above 5,000 m and it was breathtaking in every sense. The logistics were flawless, food was great, and views unforgettable!",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Sandeep Thapa",
            role: "Adventure Blogger",
            company: "Peak Journal",
            rating: 4,
            content:
                "It was tough but well-organized. The mix of rugged terrain and cultural richness makes this route stand out. I wish we had one more rest day before the pass.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Claire Dubois",
            role: "Photographer & Runner",
            company: "Alpine Lens",
            rating: 5,
            content:
                "Capturing Tilicho Lake after a lung-busting climb was magical. This trail pushed my limits but gave back even more in beauty and soul.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Binod Lama",
            role: "Trail Run Coach",
            company: "Nepal Fastpackers",
            rating: 5,
            content:
                "Highly recommended for serious runners. This route has everything — technical challenges, high-altitude beauty, and genuine Himalayan hospitality.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Annapurna Circuit Trail: 9 Days of Running and Challenge"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is moderate level with decent uphill climb and has to cover long distances during the program. As you will cross the altitude of over 5000+ meters it is important that you are physically fit for this trail running program. This program is recommended for the person who has certain trekking/running experience over 4000+ m. The accommodations in this area are decent - remote with increase in altitude.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Annapurna Circuit Trail Running – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedAnnapurnaCircuitItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Annapurna Circuit Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining /> */}
                        {/* <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={mansaluSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default AnnapurnaCircuitTrailRunning