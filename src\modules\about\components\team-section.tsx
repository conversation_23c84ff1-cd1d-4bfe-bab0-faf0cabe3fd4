'use client';

import Image from 'next/image';
import { useState } from 'react';

interface TeamMember {
    name: string;
    title: string;
    photo: string;
    video?: string;
    bio: string;
    tags: string[];
}

const team: TeamMember[] = [
    {
        name: "Mrs. <PERSON><PERSON><PERSON>",
        title: "Chairman",
        photo: "/images/review/1.png",
        video: "/videos/review/1.mp4",
        bio: "Chairman of Mustang Airworks, leading strategic vision and business development.",
        tags: ["Leader", "Visionary", "Inspiring"],
    },
    {
        name: "Mr. <PERSON><PERSON><PERSON><PERSON>",
        title: "Managing Director / Accountable Manager",
        photo: "/images/review/2.png",
        video: "/videos/review/2.mp4",
        bio: "Managing Director with vast industry experience overseeing company operations and accountability.",
        tags: ["Experienced", "Accountable", "Strategic"],
    },
    {
        name: "Mr. <PERSON>",
        title: "Quality Assurance Manager",
        photo: "/images/review/3.png",
        video: "/videos/review/3.mp4",
        bio: "Ensures highest quality standards across all products and services, focusing on compliance and excellence.",
        tags: ["Detail-Oriented", "Quality-Focused", "Committed"],
    },
    {
        name: "Mr. <PERSON><PERSON>",
        title: "Material Inspector / Store-Incharge",
        photo: "/images/review/4.png",
        video: "/videos/review/4.mp4",
        bio: "Leads inspection and inventory management, maintaining accuracy and standards in material handling.",
        tags: ["Reliable", "Organized", "Diligent"],
    },
    {
        name: "Mr. Machindra Sapkota",
        title: "Finance Officer",
        photo: "/images/review/5.png",
        video: "/videos/review/1.mp4",
        bio: "Responsible for financial accounting, reporting, and fiscal management for Mustang Airworks.",
        tags: ["Analytical", "Trustworthy", "Precise"],
    },
    {
        name: "Ms. Manisha Bhat",
        title: "Receptionist",
        photo: "/images/review/6.png",
        video: "/videos/review/2.mp4",
        bio: "Front desk representative providing support and communication coordination for the company.",
        tags: ["Friendly", "Helpful", "Professional"],
    },
];

export default function OurTeamSection() {
    const [selected, setSelected] = useState<TeamMember | null>(null);

    return (
        <section className="py-14 bg-gray-50">
            <div className="container mx-auto px-4">
                <h2 className="text-3xl flex items-center justify-center font-extrabold mb-8 text-secondary">Meet the Team</h2>
                <div className="flex items-center justify-center overflow-x-auto pb-4 pt-4">
                    {team.map((member, i) => (
                        <div
                            key={i}
                            className={`group relative w-56 flex-shrink-0 ${i !== 0 ? '-ml-6' : ''}  `}
                            style={{ overflow: 'visible' }}
                        >
                            <div
                                className={`relative z-0 rounded-xl bg-white shadow-md cursor-pointer overflow-hidden ring-2 ring-brand/40 transition-transform duration-300 ease-out group-hover:ring-brand group-hover:scale-105 ${i % 2 === 0 ? 'rotate-1' : '-rotate-1'} group-hover:-rotate-1 group-hover:z-20 `}
                                onClick={() => setSelected(member)}
                            >
                                <div className="relative w-full h-72 overflow-hidden">
                                    <Image
                                        src={member.photo}
                                        alt={member.name}
                                        fill
                                        className="w-full h-full object-cover transition-opacity duration-300 group-hover:opacity-0"
                                    />
                                    {member.video && (
                                        <video
                                            src={member.video}
                                            className="absolute inset-0 w-full h-full object-cover rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                                            muted
                                            autoPlay
                                            loop
                                            playsInline
                                        />
                                    )}

                                    <div
                                        className={`absolute top-1/2 right-0 -translate-y-1/2 translate-x-4 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-30 `}
                                    >
                                        <span className=" bg-white shadow-lg text-secondary font-semibold text-xs px-4 py-2 rounded-full border  border-gray-200 ">
                                            More about {member.name.split(' ').slice(0, 2).join(' ')}
                                        </span>
                                    </div>
                                </div>

                            </div>
                        </div>
                    ))}
                </div>

                {selected && (
                    <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
                        <div className="bg-white rounded-xl shadow-lg p-6 max-w-3xl w-full relative">

                            {/* Close Button */}
                            <button
                                className="absolute top-3 right-3 text-gray-500 hover:text-primary text-lg"
                                onClick={() => setSelected(null)}
                                aria-label="Close"
                            >
                                ×
                            </button>

                            {/* Side-by-side layout */}
                            <div className="flex flex-col md:flex-row gap-6">

                                {/* Left: Video or Photo */}
                                <div className="md:w-1/2">
                                    {selected.video ? (
                                        <video
                                            src={selected.video}
                                            className="w-full h-64 md:h-full object-cover rounded-xl"
                                            autoPlay
                                            muted
                                            loop
                                            playsInline
                                        />
                                    ) : (
                                        <Image
                                            src={selected.photo}
                                            alt={selected.name}
                                            width={400}
                                            height={400}
                                            className="w-full h-64 md:h-full object-cover rounded-xl"
                                        />
                                    )}
                                </div>

                                {/* Right: Content */}
                                <div className="md:w-1/2 flex flex-col justify-center">
                                    <h3 className="text-2xl font-bold mb-2 text-secondary">{selected.name}</h3>
                                    <p className="text-sm text-gray-700 mb-3">{selected.title}</p>
                                    <p className="text-gray-600 mb-4">{selected.bio}</p>
                                    <div className="flex flex-wrap gap-2">
                                        {selected.tags.map(tag => (
                                            <span
                                                key={tag}
                                                className="px-3 py-1 rounded-full bg-primary/10 text-primary text-xs font-medium"
                                            >
                                                {tag}
                                            </span>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

            </div>
        </section >
    );
}