import PackageCard from '@/components/common/cards/card'
import { Package } from '@/types/package'
import Link from 'next/link'
import React from 'react'

const trailRunningPackages: Package[] = [
    {
        id: 1,
        slug: "trail-running-pokhara",
        title: "Trail Running Around Pokhara",
        image: "/images/fastpacking/hero/image1.webp",
        duration: "4 Days",
        currency: "$",
        days: 4,
        originalPrice: 399,
        currentPrice: 299,
        priceUnit: "pp",
        difficulty: "Easy",
        rating: 4,
    },
    
]

const TrailRunningPage = () => {
    return (
        <main className="max-w-7xl mx-auto px-4 py-12">
            <h1 className="text-3xl font-bold mb-8">Trail Running</h1>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {trailRunningPackages.map((pkg) => (
                    <Link href={`/trail-running/${pkg.slug}`} key={pkg.id}>
                        <PackageCard key={pkg.id} package={pkg} />
                    </Link>

                ))}
            </div>
        </main>
    )
}

export default TrailRunningPage
