'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedKhumaiDandaItinerary } from '@/data/trekking/khumai-danda/detailed-itinerary'
import { khumaiDandaSection } from '@/data/trekking/khumai-danda/package-info'

const KhumaiDandaTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Lumre - Pokhara",
        accommodation: "Tea Houses",
        duration: "Approx 6-7 hrs daily/Can vary",
        maxElevation: "Korchan Danda (3,682 m)",
        group: "2–10 max",
        region: "Annapurna Region",
        type: "Unpaved and Muddy",
        bestSeason: "March–May & September–November",
        grade: "Easy – Moderate",
    };



    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Enjoy the panoramic view of Mt. Macchapuchre (6993m), Annapurna (8091m) and Mardi (5587m)" },
        { text: "Trek through less crowded and peaceful trails along the lush forest" },
        { text: "Warm hospitality of the locals and explore their traditional culture" },
        { text: "Beautiful view of Sunset and Sunrise from Korchon" },
    ];


    const content = {
        paragraphs: [
            `The Khumai Danda Trek is a short and off-the-beaten-path adventure in the Annapurna region, perfect for those seeking stunning mountain views without the crowds. This trek offers panoramic sights of Machhapuchhare (Fishtail), Annapurna, and Mardi Himal, along with peaceful forest trails.

        The Khumai Danda Trek offers beautiful terraced fields, waterfalls, green hills, and amazing mountain views. Along the way, trekkers get a chance to learn about the diverse cultures and lifestyles of local ethnic communities such as the Gurung, Magar, Tamang, Newar, and Brahmin. It’s a perfect blend of natural beauty and cultural exploration.
        `,
        ],
    };

    const note = {
        paragraphs: [
            `Although the Khumai Danda Trek is categorized as easy to moderate, some uphill sections and long walks require decent physical fitness. Trekking poles and good shoes are recommended.`,
            `Weather in the region can change quickly. Pack light but be prepared with rain gear and warm layers, especially during early mornings and evenings.`,
        ],
    };

    const briefing = {
        paragraphs: [
            `Before your departure, we will organize a short online or in-person briefing session where you’ll meet your trek leader and get insights into the itinerary, gear checklist, altitude tips, and cultural dos and don’ts. This ensures a well-informed and safe trekking experience.`,
        ],
    };



    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional Guide and Porters, their food, accommodation, and insurance.",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 meals per day which includes Breakfast, Lunch and Dinner.",
                "Medicines and water purifiers will be provided.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "Permits and any other necessary documents.",
            ],
        },
        {
            title: "Taxes & Certificates",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government and service tax.",
            ],
        },
    ];



    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipment (sleeping bag, trekking shoes, headlamp, etc.)",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Porter",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,400 m)" },
        { day: 2, title: "Kathmandu → Pokhara (822 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 3, title: "Pokhara → Lumre Drive → Trek to Kaltha (1,890 m) [↑ 1,068 m, ~6 km, 3–4 hrs trek]" },
        { day: 4, title: "Kaltha → Meshroom (2,900 m) [↑ 1,010 m, ~7 km, ~5 hrs]" },
        { day: 5, title: "Meshroom → Korchan Danda (3,682 m) → Khumai Danda (3,245 m) [↑ 345 m / ↓ 437 m, ~10 km, ~7–8 hrs]" },
        { day: 6, title: "Khumai Danda → Ghachowk → Drive to Pokhara (822 m) [↓ 2,423 m, ~5–6 hrs trek + 2 hrs drive]" },
        { day: 7, title: "Pokhara → Kathmandu (1,400 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 8, title: "Final Departure from Kathmandu" },
    ];

    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Bishal Gurung",
            role: "First-Time Trekker",
            company: "Solo Traveler",
            rating: 5,
            content:
                "Khumai Danda was my first trek and it exceeded expectations. The view from Korchan Danda was absolutely stunning! A peaceful trail, perfect for beginners.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Emily Ross",
            role: "Photographer",
            company: "Wander Shots",
            rating: 5,
            content:
                "Loved the forest trails and the cultural experience through local villages. Sunrise from Korchan was magical. Highly recommended for nature lovers!",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Sanjay Tamang",
            role: "Outdoor Guide",
            company: "Nepal Trek Tours",
            rating: 4,
            content:
                "Easy trail, rich culture, and great views. A few steep sections but overall manageable. A great short trek for a weekend escape from city life.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Nina Patel",
            role: "Travel Blogger",
            company: "Roaming Souls",
            rating: 5,
            content:
                "One of the best budget-friendly hikes in Nepal. I met such kind people in the villages and the trail had barely any crowd. Very peaceful!",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Ramesh Adhikari",
            role: "Adventure Seeker",
            company: "Freelancer",
            rating: 5,
            content:
                "This trek is short but offers big rewards. From rhododendron forests to snowy peaks — it’s perfect if you want both nature and culture in one trip.",
            avatar: "/images/review/5.png",
        },
    ];

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Khumai Danda Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Khumai Danda Trek is a short, scenic, and beginner-friendly adventure in the Annapurna region. This off-the-beaten-path route offers stunning views of Machhapuchhare, Mardi Himal, and Annapurna South. With peaceful trails, rich forests, and authentic village hospitality, it&apos;s perfect for those seeking natural beauty and cultural immersion without the crowds. A great option for first-time trekkers or those looking for a refreshing multi-day escape close to Pokhara.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Khumai Danda Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedKhumaiDandaItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/map/trekking/khumai-danda-trek.webp"
                            altText="Khumai Danda Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                         />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={khumaiDandaSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default KhumaiDandaTrek
