'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, Bus, FileText, HandCoins, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedDhorpatanItinerary } from '@/data/trekking/dhorpatan/itinerary-detailed'
import { dhorpatanSection } from '@/data/trekking/dhorpatan/package-info'

const DhorpatanTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Takam Village - Pokhara",
        accommodation: "Tea Houses and Camps",
        duration: "Approx 6-7 hrs daily/Can vary",
        maxElevation: "Dhorpatan Hunting Reserve (2850 - 5500 m)",
        group: "2–10 max",
        region: "West Nepal (Dhorpatan Region)",
        type: "Unpaved and Muddy",
        bestSeason: "March–May & September–November",
        grade: "Difficult",
    };



    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };


    const items: HighlightItem[] = [
        { text: "Amazing landscape from the Dhorpatan valley." },
        { text: "A lush, thriving forest with medicinal herbs and rich diverse wildlife." },
        { text: "Explore the outskirts of Dhorpatan, observing the valleys and Uttar Ganga river." },
        { text: "Witness elusive wildlife like snow leopards, musk deer, red panda, and blue sheep." },
        { text: "Close-up views of Dhaulagiri and Annapurna as well as lesser-seen peaks in the west of Nepal." },
        { text: "Interact with the local people living in the Dhorpatan region and learn about their culture." },
    ];

    const content = {
        paragraphs: [
            `Dhorpatan Hunting Reserve aka west Nepal is rural and unexplored yet one of the best trekking destinations in Nepal. Dhorpatan Trek offers you amazing greenery and beautiful landscape. You will see various wild animals in the Dhorpatan area. The wildlife, the vegetation, the mountain views, and the ever-so-friendly people makes the Dhorpatan Trek the most exciting trail in Nepal. Dhorpatan Hunting Reserve, the only hunting reserve in Nepal, stretches across 1,325 square kilometers, covering parts of Baglung, Rukum, and Myagdi districts.

        Explore the rich and unique culture of the rural communities nestled in the Dhorpatan region and Gurja Khani. This journey takes you deep into remote Himalayan villages where ancient traditions still shape daily life. Discover how the locals sustainably live off the land—through traditional farming, herding, and the use of medicinal herbs harvested from the dense surrounding forests. More than just a hike, this experience offers an intimate glimpse into a way of life untouched by time. With its cultural depth, natural beauty, and warm hospitality, this will undoubtedly be one of the most memorable treks of your lifetime.`
        ]
    };



    const note = {
        paragraphs: [
            "Expect long walking hours through remote forested areas and steep ascents in high altitudes. Physical fitness and strong mental preparedness are essential.",
            "Basic tea house accommodations are available in a few villages, while other nights may require camping in the wild. Come prepared for unpredictable weather and self-reliance."
        ]
    };



    const briefing = {
        paragraphs: [
            "Before your trek begins, a comprehensive online briefing will be conducted. This session includes packing advice, trail safety, route overview, and altitude protocols. You’ll also meet your guide virtually and have the opportunity to ask questions about the Dhorpatan region and trek logistics."
        ]
    };



    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional guide and porters, including their food, accommodation, salary, and insurance.",
            ],
        },
        {
            title: "Meals & Water",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 meals per day (Breakfast, Lunch, and Dinner).",
                "Medicines and water purifiers will be provided during the trek.",
            ],
        },
        {
            title: "Permits & Documentation",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All required permits and necessary documents for the Dhorpatan Trek.",
            ],
        },
        {
            title: "Transportation",
            icon: <Bus className="w-5 h-5 text-dark" />,
            items: [
                "Pickup from Pokhara to Takam and return transfer from Dhorpatan to Pokhara.",
            ],
        },
        {
            title: "Taxes & Charges",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government taxes, service charges, and VAT.",
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food or drinks not included in the 3 daily meals.",
                "Snacks, bottled drinks, Wi-Fi, charging fees, and other personal purchases.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal trekking gear (e.g., sleeping bag, boots, rain jacket, headlamp, etc.).",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the guide and porters (optional but appreciated).",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Costs due to unforeseen events such as illness, bad weather, or natural disasters.",
            ],
        },
        {
            title: "Other Exclusions",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Anything not explicitly mentioned in the 'What's Included' section.",
            ],
        },
    ];





    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,400 m)" },
        { day: 2, title: "Kathmandu → Pokhara (822 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 3, title: "Pokhara → Takam Village (1,665 m) [↑ 843 m, ~120 km, 6–7 hrs]" },
        { day: 4, title: "Takam → Lulang (2,450 m) [↑ 785 m, ~13 km, 4–5 hrs]" },
        { day: 5, title: "Lulang → Gurjakhani (2,650 m) [↑ 200 m, ~12 km, ~5 hrs]" },
        { day: 6, title: "Gurjakhani → Rugachaur (3,800 m) [↑ 1,150 m, ~9.5 km, 6–7 hrs]" },
        { day: 7, title: "Rugachaur → Gurjaghat (3,019 m) [↓ 781 m, ~8.4 km, ~5 hrs]" },
        { day: 8, title: "Gurjaghat → Dhorpatan (2,850 m) [↓ 169 m, ~9 km, 3–4 hrs]" },
        { day: 9, title: "Dhorpatan → Pokhara (822 m) [↓ 2,028 m, ~6–7 hrs drive]" },
        { day: 10, title: "Pokhara → Kathmandu (1,400 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 11, title: "Final Departure from Kathmandu" },
    ];




    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Binita Tamang",
            role: "Adventure Photographer",
            company: "Binita Visuals",
            rating: 5,
            content:
                "Dhorpatan blew my mind! It’s remote, raw, and perfect for anyone looking to disconnect and find peace. I captured some of my most powerful Himalayan shots in Rugachaur and Gurja Khani.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Alex Morton",
            role: "Solo Backpacker",
            company: "Wander Without Borders",
            rating: 5,
            content:
                "The trail was quiet, the villages untouched, and the people—genuinely warm and welcoming. You won’t find this kind of authenticity on more commercial routes.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Pema Gurung",
            role: "Nature Conservationist",
            company: "WildNepal",
            rating: 4,
            content:
                "Saw a musk deer and pheasants deep in the forests! The biodiversity of the reserve is real. Terrain is tough, but it’s worth every step for nature lovers.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Julie Moreau",
            role: "Ethnographer",
            company: "Cultural Mapping Project",
            rating: 5,
            content:
                "Staying in Gurja Khani was like stepping into a living museum. The cultural richness, ancient practices, and hospitality made this trek unforgettable.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Ramesh Sharma",
            role: "Trekking Enthusiast",
            company: "Freelancer",
            rating: 4,
            content:
                "It’s not for everyone — you’ll need grit and gear. But if you want solitude and soul, Dhorpatan is Nepal’s hidden gem.",
            avatar: "/images/review/5.png",
        },
    ];




    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Dhorpatan Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Dhorpatan Trek is a remote and culturally rich journey through Nepal’s only hunting reserve. Offering dramatic landscapes, traditional villages, and diverse wildlife, it’s perfect for those seeking raw Himalayan adventure off the beaten path.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Dhorpatan Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedDhorpatanItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/map/trekking/dhorpatan-trek.webp"
                            altText="Dhorpatan Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={dhorpatanSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div >
            <FAQSection />
        </>
    )
}

export default DhorpatanTrek