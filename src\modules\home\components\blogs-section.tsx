"use client"

import React from "react"
import Link from "next/link"
import { blogPosts, type BlogPost } from "@/data/blog"
import BlogCard from "@/components/common/cards/blog/blog-card"

export default function BlogsSection() {

    const recentPosts: BlogPost[] = blogPosts.slice(0, 3)

    // const router = useRouter()

    // const handleReadMore = (slug: string) => {
    //     router.push(`/blogs/${slug}`)
    // }

    return (
        <section className="mb-24 md:mb-16">
            <div className="container mx-auto px-4">
                <h2 className="md:text-4xl text-3xl font-bold text-dark text-center mb-12">
                    Recent Blogs
                </h2>

                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                    {recentPosts.map((post) => (
                        <BlogCard
                            key={post.id}
                            blog={post}
                        />
                    ))}
                </div>

                {/* View All CTA */}
                <div className="mt-12 text-center">
                    <Link
                        href="/blogs"
                        className="inline-block bg-white border border-gray-300 hover:bg-gray-50 text-gray-800 font-medium px-6 py-3 rounded-lg transition"
                    >
                        View All Blogs
                    </Link>
                </div>
            </div>
        </section>
    )
}


// "use client"

// import React from "react"
// import Image from "next/image"
// import Link from "next/link"
// import { Button } from "@/components/ui/button"

// interface BlogPost {
//     id: number
//     title: string
//     excerpt: string
//     image: string
//     tags: string[]
//     href: string
// }

// const posts: BlogPost[] = [
//     {
//         id: 1,
//         title: "Fastpacking the Himalayas: Speed Meets Adventure",
//         excerpt:
//             "Discover the thrill of fastpacking through the rugged trails of Himachal Pradesh. From steep ascents to panoramic ridge walks, this guide covers everything you need to prepare for a lightweight, high-speed trek through some of India’s most breathtaking mountain terrain.",
//         image: "/images/fastpacking/hero/image1.webp",
//         tags: ["fastpacking", "treks", "himachal", "hiking", "gear"],
//         href: "/blog/fastpacking-the-himalayas",
//     },
//     {
//         id: 2,
//         title: "Winter Trails in Uttarakhand: A Frozen Wonderland",
//         excerpt:
//             "Explore the magic of winter trekking in Uttarakhand. This blog dives into the top winter trails like Kedarkantha and Brahmatal, essential gear recommendations, safety tips, and how to make the most of snowy landscapes on your next adventure.",
//         image: "/images/fastpacking/hero/image2.webp",
//         tags: ["winter trek", "uttarakhand", "snow", "gear", "mountains"],
//         href: "/blog/winter-trails-uttarakhand",
//     },
//     {
//         id: 3,
//         title: "Top 5 Trekking Essentials for Every Trail Explorer",
//         excerpt:
//             "Before you hit the trail, make sure your backpack is equipped with these five must-have items. From reliable water filtration to the best trail snacks, we’ve rounded up gear that can make or break your trekking experience.",
//         image: "/images/fastpacking/hero/image3.webp",
//         tags: ["gear", "tips", "backpacking", "safety", "preparation"],
//         href: "/blog/top-5-trekking-essentials",
//     },
// ]

// export default function BlogsSection() {
//     return (
//         <section className="mb-24 md:mb-16">
//             <div className="container mx-auto px-4">
//                 <h2 className="md:text-4xl text-3xl font-bold text-dark text-center mb-12">
//                     Recent Blogs
//                 </h2>

//                 <div className="space-y-8">
//                     {posts.map((post) => (
//                         <article
//                             key={post.id}
//                             className="flex flex-col md:flex-row bg-gradient-to-r from-white via-white to-brand/20 rounded-lg shadow-lg overflow-hidden hover:shadow-2xl transition-shadow duration-300"
//                         >
//                             <div className="relative h-48 md:h-auto md:w-1/3 flex-shrink-0">
//                                 <Image
//                                     src={post.image}
//                                     alt={post.title}
//                                     fill
//                                     className="object-cover"
//                                 />
//                             </div>

//                             {/* Content */}
//                             <div className="p-6 flex flex-col justify-between md:w-2/3">
//                                 <div>
//                                     <h3 className="text-xl font-semibold text-gray-800 mb-3">
//                                         {post.title}
//                                     </h3>
//                                     <p className="text-gray-600 leading-relaxed mb-4">
//                                         {post.excerpt}
//                                     </p>
//                                 </div>

//                                 {/* Tags + Read More */}
//                                 <div className="flex flex-wrap items-center justify-between">
//                                     <div className="flex flex-wrap gap-2">
//                                         {post.tags.map((tag) => (
//                                             <span
//                                                 key={tag}
//                                                 className="bg-secondary/30 text-dark text-sm px-3 py-1 rounded-full"
//                                             >
//                                                 {tag}
//                                             </span>
//                                         ))}
//                                     </div>
//                                     <Link
//                                         href={post.href}
//                                     >
//                                         <Button className="mt-4 md:mt-0 inline-block text-white text-sm font-medium px-5 py-2 rounded-lg transition">
//                                             Read More
//                                         </Button>
//                                     </Link>
//                                 </div>
//                             </div>
//                         </article>
//                     ))}
//                 </div>

//                 {/* View All CTA */}
//                 <div className="mt-12 text-center">
//                     <Link
//                         href="/blogs"
//                         className="inline-block bg-white border border-gray-300 hover:bg-gray-50 text-gray-800 font-medium px-6 py-3 rounded-lg transition"
//                     >
//                         View All Blogs
//                     </Link>
//                 </div>
//             </div>
//         </section>
//     )
// }
