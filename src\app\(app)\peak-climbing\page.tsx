import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Mountain } from "lucide-react";
import Link from "next/link";

const PeakClimbingPage = () => {
  return (
    <div className="">
      <main className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          {/* Hero Section */}
          <div className="mb-12">
            <div className="relative inline-block mb-6">
              <div className="flex items-center justify-center gap-4 mb-4">
                <Mountain className="w-16 h-16 text-orange-500" />
                <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                <Mountain className="w-12 h-12 text-green-500" />
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse delay-300"></div>
                <Mountain className="w-20 h-20 text-orange-400" />
              </div>
            </div>

            <h1 className="text-5xl md:text-6xl font-bold text-gray-800 mb-6">
              Peak Climbing is 
              <span className="block text-orange-500">Coming Soon</span>
            </h1>

            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              We&apos;re crafting an extraordinary new adventure experience that will take your trekking journey to new
              heights. Get ready to explore uncharted territories!
            </p>
          </div>


          {/* Call to Action */}
          <div className="bg-gradient-to-r from-secondary to-brand rounded-2xl p-8 text-white">
            <h2 className="text-3xl font-bold mb-4">Ready for Your Next Adventure?</h2>
            <p className="text-xl mb-6 opacity-90">While you wait, explore our current amazing trekking experiences!</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild className="bg-white text-orange-600 hover:bg-gray-100">
                <Link href="/trekking" className="flex items-center gap-2">
                  Explore Current Treks
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </div>          
        </div>
      </main>
    </div>
  );
};

export default PeakClimbingPage;