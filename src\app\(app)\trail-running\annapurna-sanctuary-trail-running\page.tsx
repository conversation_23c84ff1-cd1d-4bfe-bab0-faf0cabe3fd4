'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, Car, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedAnnapurnaSanctuaryItinerary } from '@/data/trailrunning/annapurna-circuit/sanctuary-itinerary-detailed'
import { annapurnaSanctuarySection } from '@/data/trailrunning/annapurna-circuit/sanctuary-package'

const AnnapurnaSanctuaryTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Hile – Pokhara',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 6–7 hrs daily / Can vary',
        maxElevation: '4,200 m (Mardi Base Camp)',
        group: '2–6',
        region: 'Annapurna Region',
        type: 'Mixture of Paved and Unpaved paths',
        bestSeason: 'March to May and September to November',
        grade: 'Moderate (Includes steep uphill/downhill and long distances)',
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Closeup view of mighty peaks of Nepal i.e Annapurna (8091 m), Machhapuchhre (6993 m) and Mardi Himal (5587 m).' },
        { text: 'Rhododendron Bloom during the season.' },
        { text: 'Enjoy the Hot Spring at Jhinu Danda to recover from fatigue.' },
        { text: 'Explore Ghandruk village, a very famous Gurung village with their rich cultural history.' },
        { text: 'Reach the Annapurna Base Camp at 4130 m, Mardi Base Camp at 4500 m.' },
        { text: 'Explore Lwang village, a very famous village for growing tea.' },
        { text: 'Extraordinary view from Badal Danda.' },
    ];


    const content = {
        paragraphs: [
            `This 10 days trail running programs will take you to famous places like Machhapuchhre Base Camp(MBC), Annapurna Base Camp(ABC), Jhinu Danda, Mardi Base Camp etc. The program is especially designed for trail runners who can easily run steep uphill and downhill to cover more distance in less duration. The breathtaking view of Annapurna, Mardi, Machhapuchhre will make this program memorable for your lifetime.

    The program begins from hile and ends at Pokhara covering famous trekking destinations along the way. The extreme uphill and steep downhill, lush forests, culturally enriched villages, closeup mountains view will make this journey exciting among experienced trail runners. This trail will cover a mixture of paved, unpaved and muddy roads.`,
        ],
    }


    const note = {
        paragraphs: [
            `This trail begins from Hile after a short drive from Pokhara and ends at Lakeside, Pokhara on Day 10. Daily running durations average 6–7 hours depending on terrain and pace.`,
            `Trail conditions vary from muddy paths and steep ascents to stone steps and forested descents, so ensure you’re trained for both endurance and elevation.`,
        ],
    };


    const briefing = {
        paragraphs: [
            `After you sign up, we’ll provide a full online briefing explaining the itinerary, trail conditions, gear checklist, and safety precautions. This ensures you're fully prepared before you hit the trails.`,
        ],
    };

    const myInclusions: Category[] = [
        {
            title: 'Guides & Porters',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.',
                'Porter for each 2 person',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                'We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All the necessary paperwork and Annapurna conservation entry permit ( ACAP permit & TIMS card etc.)',
            ],
        },
        {
            title: 'Transportation',
            icon: <Car className="w-5 h-5 text-dark" />,
            items: [
                'Transportation from Pokhara to Hile and Phedi to Pokhara.',
            ],
        },
        {
            title: 'Accommodation',
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                'Lodge and tea house accommodations along the trail.',
            ],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with an oximeter.',
                'Trek achievement certificate after the trek.',
                'Water bottle to store hot water overnight.',
                'Government taxes, TDS, VAT & other legal documents.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any other food/drink should be covered by you, except (3 meals) provided by the company.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                'Personal equipments',
            ],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                'Tips for the Guide and Potter',
            ],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                'Any expenses that are not in the included section.',
            ],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Drive to Hile → Run to Ghorepani (2,874 m), 11 km, approx 2–3 hrs [↑ 1,334 m]" },
        { day: 2, title: "Run Ghorepani → Tadapani (2,610 m), 14 km, approx 3 hrs [↓ 264 m]" },
        { day: 3, title: "Run Tadapani → Bamboo (2,310 m), approx 17 km, approx 3–4 hrs [↓ 300 m]" },
        { day: 4, title: "Run Bamboo → Machhapuchhre Base Camp (3,700 m), 10.6 km, approx 2 hrs [↑ 1,390 m]" },
        { day: 5, title: "Run MBC → Annapurna Base Camp (4,130 m) → Chhomrong (2,170 m), 23 km, approx 5–6 hrs [↑ 430 m / ↓ 1,530 m]" },
        { day: 6, title: "Run Chhomrong → Jhinu Danda → Ghandruk (2,012 m), 9.6 km, approx 2 hrs [↓ 158 m]" },
        { day: 7, title: "Run Ghandruk → Badal Danda (3,210 m), 12.5 km, approx 2.5 hrs [↑ 1,198 m]" },
        { day: 8, title: "Run Badal Danda → Mardi Base Camp (4,200 m) → High Camp (3,550 m), 14 km, approx 4 hrs [↑ 1,290 m / ↓ 340 m]" },
        { day: 9, title: "Run High Camp → Lwang Village (1,550 m), approx 20 km, approx 5 hrs [↓ 2,000 m]" },
        { day: 10, title: "Run Lwang → Dhampus → Phedi (822 m) → Drive to Pokhara, 28 km, approx 5 hrs [↓ 728 m]" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Tenzing Rai",
            role: "Trail Running Guide",
            company: "Himalaya Endurance Co.",
            rating: 5,
            content:
                "The Annapurna Sanctuary trail combines raw altitude, steep terrain, and iconic base camps. This is the real deal for elite trail runners seeking a true Himalayan experience.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Anita Shrestha",
            role: "Physiotherapist & Runner",
            company: "Peak Motion Nepal",
            rating: 5,
            content:
                "Running to ABC and MBC pushed me to my limits—but it was all worth it. The trails, the food, the locals, the vibe—it was all flawless. I’ll remember this forever.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Bruno Keller",
            role: "Mountain Athlete",
            company: "Swiss Trail Union",
            rating: 4,
            content:
                "Technically demanding, physically brutal, and visually mesmerizing. I’d love a few more acclimatization stops but overall an amazing route for prepared athletes.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Rinzin Lhamo",
            role: "Adventure Filmmaker",
            company: "Nomad Lens Studio",
            rating: 5,
            content:
                "Shooting footage while running the trail was magical. MBC and Mardi Base Camp were absolutely cinematic! Logistics were super smooth and crew was fantastic.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Danielle Fox",
            role: "Running Coach",
            company: "TrailLab UK",
            rating: 5,
            content:
                "This isn’t a casual jog—it’s a mountain mission! Every day was tough and rewarding. Highly recommend to experienced runners who crave high-altitude, high-grit adventure.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="10 Days - Himalayan Trail Escape – Annapurna Sanctuary Trail Run "
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is moderate level with decent uphill climb and has to cover long distances during the program. If you are healthy and physically fit, you will probably be able to complete this program. The accommodations in this area are decent-remote with increase in altitude.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Mansalu Circuit – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedAnnapurnaSanctuaryItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Annapurna Sanctuary Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={annapurnaSanctuarySection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default AnnapurnaSanctuaryTrailRunning