import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, Award, Shield, Heart, MapPin } from "lucide-react"
import Image from "next/image"

export default function AboutPage() {
  return (
    <div className="min-h-screen">

      <section className="relative h-screen flex items-center justify-center">
        <Image
          src="/images/fastpacking/hero/image1.webp"
          alt="Mountains in the mist"
          fill
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-80 object-cover"

        />
        <div className="relative z-10 text-center text-white max-w-4xl mx-auto px-4">
          <h1 className="text-5xl font-bold mb-4">About Trail Treks</h1>
          <p className="text-xl opacity-90">Your trusted partner for extraordinary Himalayan adventures since 2015</p>
        </div>
      </section>

      <main className="container mx-auto px-4 py-16">
        <section className="mb-20">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                Born from a deep passion for the mountains, Trail Treks began as a dream to share the raw beauty and
                transformative power of the Himalayas with adventurers worldwide. What started as weekend trail runs in
                the foothills has evolved into Nepal&apos;s premier adventure company.
              </p>
              <p className="text-lg text-gray-700 mb-6 leading-relaxed">
                We believe that every step on the trail is a step toward discovering your true potential. Our mission is
                to guide you safely through some of the world&apos;s most spectacular landscapes while pushing your limits
                and creating memories that last a lifetime.
              </p>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary" className="text-sm">
                  Est. 2015
                </Badge>
                <Badge variant="secondary" className="text-sm">
                  1000+ Adventures
                </Badge>
                <Badge variant="secondary" className="text-sm">
                  50+ Peaks
                </Badge>
              </div>
            </div>
            <div className="relative h-96">
              <Image
                src="/images/fastpacking/hero/image2.webp"
                alt="Trail Treks team guiding adventurers"
                fill
                className="rounded-lg shadow-lg w-full"
              />
            </div>
          </div>
        </section>

        {/* Our Values */}
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">What Drives Us</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center p-6 hover:shadow-2xl">
              <CardContent className="pt-6 ">
                <Shield className="h-12 w-12 text-brand mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">Safety First</h3>
                <p className="text-gray-600">
                  Every adventure begins with comprehensive safety protocols. Our certified guides carry emergency
                  equipment and maintain constant communication with base camp.
                </p>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-2xl">
              <CardContent className="pt-6 ">
                <Heart className="h-12 w-12 text-brand mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">Authentic Experiences</h3>
                <p className="text-gray-600">
                  We create genuine connections with local communities and pristine wilderness, ensuring each journey
                  enriches both adventurers and the places we visit.
                </p>
              </CardContent>
            </Card>
            <Card className="text-center p-6 hover:shadow-2xl">
              <CardContent className="pt-6 ">
                <Award className="h-12 w-12 text-brand mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">Excellence</h3>
                <p className="text-gray-600">
                  From route planning to gear selection, we maintain the highest standards in every aspect of your
                  adventure, ensuring unforgettable experiences.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Team Section
        <section className="mb-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Meet Our Guides</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="overflow-hidden">
              <Image src="/himalayan-guide-smiling.png" alt="Pemba Sherpa" className="w-full h-48 object-cover" />
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-2">Pemba Sherpa</h3>
                <p className="text-brand font-medium mb-3">Lead Mountain Guide</p>
                <p className="text-gray-600 text-sm">
                  15+ years guiding in the Himalayas. Everest summiteer and certified wilderness first aid instructor.
                </p>
              </CardContent>
            </Card>
            <Card className="overflow-hidden">
              <Image src="/female-trail-guide.png" alt="Sarah Chen" className="w-full h-48 object-cover" />
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-2">Sarah Chen</h3>
                <p className="text-brand font-medium mb-3">Trail Running Specialist</p>
                <p className="text-gray-600 text-sm">
                  Ultra-marathon champion with expertise in high-altitude trail running and endurance coaching.
                </p>
              </CardContent>
            </Card>
            <Card className="overflow-hidden">
              <Image src="/himalayan-trekking-guide.png" alt="Raj Tamang" className="w-full h-48 object-cover" />
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold mb-2">Raj Tamang</h3>
                <p className="text-brand font-medium mb-3">Cultural Guide</p>
                <p className="text-gray-600 text-sm">
                  Local historian and cultural expert, sharing the rich heritage of Himalayan communities for over a
                  decade.
                </p>
              </CardContent>
            </Card>
          </div>
        </section> */}

        {/* Stats Section */}
        <section className="bg-secondary/10 rounded-lg p-12 mb-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">Our Impact</h2>
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-brand mb-2">1,200+</div>
              <p className="text-gray-600">Adventures Completed</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-brand mb-2">98%</div>
              <p className="text-gray-600">Success Rate</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-brand mb-2">25</div>
              <p className="text-gray-600">Countries Represented</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-brand mb-2">Zero</div>
              <p className="text-gray-600">Major Incidents</p>
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Ready for Your Adventure?</h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join us in the Himalayas and discover what you&apos;re truly capable of. Every journey begins with a single step.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-brand hover:bg-green-700 text-white">
              <MapPin className="h-5 w-5 mr-2" />
              Plan Your Trek
            </Button>
            <Button size="lg" variant="outline">
              <Users className="h-5 w-5 mr-2" />
              Contact Our Team
            </Button>
          </div>
        </section>
      </main>
    </div>
  )
}
