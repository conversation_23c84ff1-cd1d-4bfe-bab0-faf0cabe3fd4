"use client"

import React, { useState, useEffect } from "react"
import Image from "next/image"
import {
    ChevronLeft,
    ChevronRight,
    Calendar as CalIcon,
    Users,
    CreditCard,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface Event {
    id: number
    title: string
    description: string
    image: string
    duration: string      // e.g. "4 days"
    participants: number  // e.g. 4
    originalPrice?: number
    price: number
}

const EVENTS: Event[] = [
    {
        id: 1,
        title: "Susa Valley Ring Trek",
        description:
            "This trek is set in Susa Valley, the valley that connects France & Italy. History, heritage, and stunning mountain scenery await.",
        image: "/images/fastpacking/hero/image1.webp",
        duration: "4 days",
        participants: 4,
        originalPrice: 699,
        price: 600,
    },
    {
        id: 2,
        title: "Running Around Pokhara",
        description:
            "Experience the lakeside city like never before with our scenic running tour—perfect for all levels!",
        image: "/images/fastpacking/hero/image2.webp",
        duration: "1 day",
        participants: 8,
        originalPrice: 50,
        price: 40,
    },
    {
        id: 3,
        title: "Sunrise Trek to Sarangkot",
        description:
            "Catch the Himalayan sunrise atop Sarangkot—early morning hike, breathtaking views!",
        image: "/images/fastpacking/hero/image3.webp",
        duration: "1 day",
        participants: 12,
        originalPrice: 60,
        price: 50,
    },
    {
        id: 4,
        title: "Full‑Moon Hill Night Hike",
        description:
            "Join us under the full moon for a guided night hike—magical starlit trails await.",
        image: "/images/fastpacking/hero/image4.webp",
        duration: "1 night",
        participants: 5,
        originalPrice: 55,
        price: 45,
    },
]

export default function FeaturedEventsCarousel() {
    const total = EVENTS.length
    const [perPage, setPerPage] = useState(2)
    const [index, setIndex] = useState(0)

    useEffect(() => {
        const calc = () => {
            setPerPage(window.innerWidth < 768 ? 1 : 2)
            setIndex(0)
        }
        calc()
        window.addEventListener("resize", calc)
        return () => window.removeEventListener("resize", calc)
    }, [])

    const maxIndex = total - perPage
    const shiftPercent = 100 / total
    const wrapperWidth = ((total / perPage) * 100).toFixed(4)

    useEffect(() => {
        const iv = setInterval(() => {
            setIndex((i) => (i < maxIndex ? i + 1 : 0))
        }, 6000)
        return () => clearInterval(iv)
    }, [maxIndex])

    return (
        <section className="py-8 md:py-16 bg-white">
            <div className="container mx-auto px-4">
                <h2 className="md:text-4xl text-3xl font-bold text-dark text-center mb-8">
                    Featured Events
                </h2>

                <div className="relative overflow-hidden">
                    <div
                        className="flex transition-transform duration-1000 ease-linear"
                        style={{
                            width: `${wrapperWidth}%`,
                            transform: `translateX(-${(index * shiftPercent).toFixed(4)}%)`,
                        }}
                    >
                        {EVENTS.map((ev) => (
                            <div
                                key={ev.id}
                                className="flex-shrink-0 p-4"
                                style={{ width: `${(100 / total).toFixed(4)}%` }}
                            >
                                <div className="group relative rounded-lg overflow-hidden shadow-lg">
                                    <div className="relative h-56 md:h-80">
                                        <Image
                                            src={ev.image}
                                            alt={ev.title}
                                            fill
                                            className="object-cover"
                                        />

                                        <div
                                            className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-50  transition-opacity duration-300"
                                        />

                                        <Button
                                            className=" absolute bottom-2 left-1/2 -translate-x-1/2 translate-y-4  opacity-0  group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300 ease-out text-white font-semibold px-6 py-2 rounded-full shadow-lg "
                                        >
                                            Join This Event
                                        </Button>
                                    </div>
                                </div>

                                <div className="mt-4">
                                    <h3 className="text-xl font-semibold mb-2">{ev.title}</h3>
                                    <p className="text-gray-700 mb-4 md:block hidden line-clamp-3">
                                        {ev.description}
                                    </p>
                                    <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
                                        <div className="flex flex-col items-center gap-1">
                                            <CalIcon className="md:w-6 md:h-6 w-4 h-4 text-brand" />
                                            <span className="text-brand">Duration</span>
                                            <p className="text-brand">{ev.duration}</p>
                                        </div>
                                        <div className="flex flex-col items-center gap-1">
                                            <Users className="md:w-6 md:h-6 w-4 h-4 text-brand" />
                                            <span className="text-brand">Participants</span>
                                            <p className="text-brand">{ev.participants}</p>
                                        </div>
                                        <div className="flex flex-col items-center gap-1">
                                            <CreditCard className="md:w-6 md:h-6 w-4 h-4 text-brand" />
                                            <span className="text-brand">Price</span>
                                            <div className="flex items-baseline gap-1">
                                                {ev.originalPrice && (
                                                    <span className="line-through opacity-75 text-xs text-brand">
                                                        €{ev.originalPrice.toFixed(2)}
                                                    </span>
                                                )}
                                                <p className="text-brand">€{ev.price.toFixed(2)}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <button
                        onClick={() => setIndex((i) => Math.max(i - 1, 0))}
                        disabled={index === 0}
                        className="absolute left-0 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white p-2 rounded-full shadow disabled:opacity-50"
                        aria-label="Previous"
                    >
                        <ChevronLeft className="w-6 h-6" />
                    </button>
                    <button
                        onClick={() =>
                            setIndex((i) => (i < maxIndex ? i + 1 : 0))
                        }
                        className="absolute right-0 top-1/2 -translate-y-1/2 bg-white/70 hover:bg-white p-2 rounded-full shadow"
                        aria-label="Next"
                    >
                        <ChevronRight className="w-6 h-6" />
                    </button>
                </div>
            </div>
        </section>
    )
}
