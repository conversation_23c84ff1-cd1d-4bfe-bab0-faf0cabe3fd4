"use client"

import { <PERSON><PERSON>ronLeft, <PERSON><PERSON>ronR<PERSON>, <PERSON>u } from "lucide-react"
import Link from "next/link"
import { useState, useRef } from "react"

import { But<PERSON> } from "@/components/ui/button"
import {
    NavigationMenu,
    NavigationMenuContent,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
    NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"
import {
    Sheet,
    SheetTrigger,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from "@/components/ui/sheet"
import Image from "next/image"
import TopBar from "./topbar"

type NavItem = {
    title: string
    href?: string
    hasDropdown?: boolean
    items?: NavItem[]
}

const navigationItems: NavItem[] = [
    {
        title: "TREKKING",
        href: "/trekking",
        hasDropdown: true,
        items: [
            { title: "Annapurna North Base Camp Trek", href: "/trekking/north-abc-trek" },
            { title: "Dhaulagiri Circuit Trek", href: "/trekking/dhaulagiri-circuit-trek" },
            { title: "Dhorpatan Trek", href: "/trekking/dhorpatan-trek" },
            { title: "Khumai Danda Trek", href: "/trekking/khumai-danda-trek" },
            { title: "Khopra Ridge Trek", href: "/trekking/khopra-ridge-trek" },
            { title: "Kori Trek", href: "/trekking/kori-trek" },
            { title: "Mansalu Circuit Trek", href: "/trekking/mansalu-circuit-trek" },
            { title: "Nar Phu Trek", href: "/trekking/nar-phu-trek" },
            { title: "Three Passes Trek", href: "/trekking/three-passes-trek" },
            { title: "Tsum Valley Trek", href: "/trekking/tsum-valley-trek" },
        ],
    },
    // {
    //     title: "FASTPACKI",
    //     href: "/fastpackin",
    //     hasDropdown: true,
    //     items: [
    //         {
    //             title: "Annapurna Region",
    //             hasDropdown: true,
    //             items: [
    //                 { title: "Annapurna Base Camp Trek", href: "/trekking/annapurna/north-abc-trek" },
    //                 { title: "Khopra Trek", href: "/trekking/annapurna/khopra-ridge-trek" },
    //             ],
    //         },
    //         {
    //             title: "Everest Region",
    //             hasDropdown: true,
    //             items: [
    //                 { title: "Three Trek", href: "/trekking/everest/three-passes-trek" },
    //                 { title: "Tsum Valley Trek", href: "/trekking/everest/tsum-valley-trek" },
    //                 { title: "Tsum Valle Trek", href: "/trekking/everest/tsum-valley-trek" },
    //                 { title: "Tsum Vall Trek", href: "/trekking/everest/tsum-valley-trek" },
    //             ],
    //         },
    //     ]
    // },
    {
        title: "TRAIL RUNNING",
        href: "/trail-running",
        hasDropdown: true,
        items: [
            { title: "Manaslu Circuit Trail Running", href: "/trail-running/manaslu-circuit-trail-running" },
            { title: "Kapuche Kori Trail Running", href: "/trail-running/kapuche-kori-trail-running" },
            { title: "4 Days Pokhara Trail Running", href: "/trail-running/4-day-pokhara-trail-running" },
            { title: "Annapurna Circuit Trail Running", href: "/trail-running/annapurna-circuit-trail-running" },
            { title: "Annapurna Foothills Pro Trail Running", href: "/trail-running/annapurna-foothills-pro-running" },
            { title: "Annapurna Foothills Trail Running", href: "/trail-running/annapurna-foothills-trail-running" },
            { title: "Annapurna Sanctuary Trail Running", href: "/trail-running/annapurna-sanctuary-trail-running" },
            { title: "Dhorpatan Trail Running", href: "/trail-running/dhorpatan-trail-running" },
            { title: "Hemja Tatopani Trail Running", href: "/trail-running/hemja-tatopani-trail-running" },
            { title: "Pokhara Trail Running", href: "/trail-running/pokhara-trail-running" },
        ],
    },
    { title: "PEAK CLIMBING", href: "/peak-climbing" },
    { title: "FASTPACKING", href: "/fastpacking" },
    { title: "FASTPACKING GEARS", href: "/fastpacking-gears" },
    { title: "BLOGS", href: "/blogs" },
]

const NestedDropdownItem = ({ item }: { item: NavItem }) => {
    const [showNested, setShowNested] = useState(false)
    const [position, setPosition] = useState({ x: 0, y: 0 })
    const itemRef = useRef<HTMLDivElement>(null)
    const timeoutRef = useRef<NodeJS.Timeout | null>(null)

    const handleMouseEnter = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }

        if (itemRef.current) {
            const rect = itemRef.current.getBoundingClientRect()
            const mainDropdown = itemRef.current.closest('[data-radix-navigation-menu-content]')
            const mainRect = mainDropdown?.getBoundingClientRect()

            setPosition({
                x: (mainRect?.right || rect.right) + 8,
                y: rect.top
            })
        }
        setShowNested(true)
    }

    const handleMouseLeave = () => {
        timeoutRef.current = setTimeout(() => {
            setShowNested(false)
        }, 150)
    }

    const handleNestedMouseEnter = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }
        setShowNested(true)
    }

    const handleNestedMouseLeave = () => {
        setShowNested(false)
    }

    const cleanup = () => {
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
        }
    }

    if (item.hasDropdown && item.items) {
        return (
            <>
                <div
                    ref={itemRef}
                    className="relative"
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                >
                    <div className="flex items-center justify-between p-3 rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer">
                        <span className="text-sm font-medium">{item.title}</span>
                        <ChevronRight className="h-4 w-4" />
                    </div>
                </div>

                {showNested && (
                    <div
                        className="fixed w-[300px] bg-white border rounded-md shadow-xl z-[9999] transition-all duration-200"
                        style={{
                            left: `${position.x}px`,
                            top: `${position.y}px`
                        }}
                        onMouseEnter={handleNestedMouseEnter}
                        onMouseLeave={handleNestedMouseLeave}
                    >
                        <div className="p-2">
                            {item.items.map((nestedItem, index) => (
                                <NavigationMenuLink asChild key={index}>
                                    <Link
                                        href={nestedItem.href ?? "#"}
                                        className="block select-none rounded-md p-3 leading-none no-underline transition-colors hover:bg-accent hover:text-accent-foreground overflow-y-auto relative"
                                        onClick={() => {
                                            setShowNested(false)
                                            cleanup()
                                        }}
                                    >
                                        <div className="text-sm font-medium">
                                            {nestedItem.title}
                                        </div>
                                    </Link>
                                </NavigationMenuLink>
                            ))}
                        </div>
                    </div>
                )}
            </>
        )
    }

    return (
        <NavigationMenuLink asChild>
            <Link
                href={item.href ?? "#"}
                className="block select-none rounded-md p-3 leading-none no-underline transition-colors hover:bg-accent hover:text-accent-foreground"
            >
                <div className="text-sm font-medium">
                    {item.title}
                </div>
            </Link>
        </NavigationMenuLink>
    )
}

export default function Component() {
    const [isOpen, setIsOpen] = useState(false)
    const [navigationStack, setNavigationStack] = useState<NavItem[]>([])

    const currentMenu = navigationStack.length === 0
        ? navigationItems
        : navigationStack[navigationStack.length - 1].items ?? []

    const handleOpenSection = (item: NavItem) => {
        if (item.items && item.items.length > 0) {
            setNavigationStack([...navigationStack, item])
        } else {
            setIsOpen(false)
        }
    }

    const handleBack = () => {
        setNavigationStack((prev) => prev.slice(0, -1))
    }

    const handleSheetOpenChange = (open: boolean) => {
        if (!open) {
            setNavigationStack([])
        }
        setIsOpen(open)
    }

    return (
        <header className="w-full sticky top-0 z-30">
            <TopBar />
            <nav className="bg-light border-b border-gray-200 px-4 py-4">
                <div className="container mx-auto flex items-center justify-between">
                    {/* Logo */}
                    <div className="flex items-center relative h-15 w-30">
                        <Link href="/" className="flex items-center gap-3">
                            <Image
                                src="/images/logo/fastpacking-logo.png"
                                alt="North Nepal Logo"
                                fill
                            />
                        </Link>
                    </div>

                    <div className="hidden lg:flex items-center gap-2">
                        <NavigationMenu>
                            <NavigationMenuList className="gap-1">
                                {navigationItems.map((item) => (
                                    <NavigationMenuItem key={item.title}>
                                        {item.hasDropdown ? (
                                            <>
                                                <NavigationMenuTrigger className="text-dark hover:text-dark/80 font-medium text-sm bg-transparent hover:bg-transparent data-[state=open]:text-dark/80">
                                                    <Link
                                                        href={item.href ?? "#"}
                                                        className="text-dark hover:text-dark/80 font-medium text-sm"
                                                    >
                                                        {item.title}
                                                    </Link>
                                                </NavigationMenuTrigger>
                                                <NavigationMenuContent>
                                                    <div className="w-[350px] p-2 max-h-[400px] overflow-y-auto relative">
                                                        <div className="grid gap-1">
                                                            {item.items?.map((sub, i) => (
                                                                <NestedDropdownItem key={i} item={sub} />
                                                            ))}
                                                        </div>
                                                    </div>
                                                </NavigationMenuContent>
                                            </>
                                        ) : (
                                            <NavigationMenuLink asChild>
                                                <Link
                                                    href={item.href ?? "#"}
                                                    className="text-dark hover:text-dark/80 font-medium text-sm"
                                                >
                                                    {item.title}
                                                </Link>
                                            </NavigationMenuLink>
                                        )}
                                    </NavigationMenuItem>
                                ))}
                            </NavigationMenuList>
                        </NavigationMenu>
                    </div>

                    {/* Desktop CTA */}
                    <div className="hidden lg:block">
                        <Link href="/customize-my-trip">
                            <Button className="bg-brand hover:bg-brand/80 text-white px-6 py-2 rounded-md font-medium">
                                Book Now
                            </Button>
                        </Link>
                    </div>

                    {/* Mobile sheet-trigger */}
                    <Sheet open={isOpen} onOpenChange={handleSheetOpenChange}>
                        <SheetTrigger asChild>
                            <Button variant="outline" size="icon" className="lg:hidden">
                                <Menu className="h-6 w-6" />
                                <span className="sr-only">Toggle menu</span>
                            </Button>
                        </SheetTrigger>

                        <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                            <SheetHeader>
                                <SheetTitle>
                                    {navigationStack.length === 0 ? (
                                        "Menu"
                                    ) : (
                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                onClick={handleBack}
                                                aria-label="Back"
                                            >
                                                <ChevronLeft className="h-5 w-5" />
                                            </Button>
                                            <span className="font-medium text-lg">
                                                {navigationStack[navigationStack.length - 1].title}
                                            </span>
                                        </div>
                                    )}
                                </SheetTitle>
                            </SheetHeader>

                            <div className="flex flex-col gap-6 p-2">
                                {currentMenu.map((item) =>
                                    item.hasDropdown && item.items && item.items.length > 0
                                        ? (
                                            <button
                                                key={item.title}
                                                type="button"
                                                onClick={() => handleOpenSection(item)}
                                                className="w-full flex justify-between items-center font-medium text-sm py-2 text-gray-700 hover:text-teal-600"
                                            >
                                                {item.title}
                                                <ChevronRight className="h-5 w-5" />
                                            </button>
                                        ) : (
                                            <Link
                                                key={item.title}
                                                href={item.href ?? "#"}
                                                onClick={() => setIsOpen(false)}
                                                className="text-gray-700 hover:text-teal-600 font-medium text-sm py-2 block"
                                            >
                                                {item.title}
                                            </Link>
                                        )
                                )}
                                <Button className="bg-brand hover:bg-brand/80 text-light w-full mt-4">
                                    Book Now
                                </Button>
                            </div>
                        </SheetContent>
                    </Sheet>
                </div>
            </nav>
        </header>
    )
}