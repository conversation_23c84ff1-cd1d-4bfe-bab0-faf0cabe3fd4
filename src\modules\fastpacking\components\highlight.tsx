import React from 'react'
import { Award, ArrowRightIcon } from 'lucide-react'

export interface HighlightItem {
    text: string
}

export interface HighlightProps {
    items: HighlightItem[]
}

const Highlight: React.FC<HighlightProps> = ({
    items,
}) => (
    <div className="mt-8">
        <div className="flex items-center gap-3 mb-6">
            <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
                <Award size={16} className="text-light" />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold text-brand">Highlights</h2>
        </div>

        {/* Items */}
        <div className="space-y-2">
            {items.map((item, idx) => (
                <div key={idx} className="flex items-start gap-3">
                    <div className="w-6 h-6 mt-1 flex-shrink-0">
                        <ArrowRightIcon size={24} className="text-gray-600" />
                    </div>
                    <p className="text-gray-700 text-lg leading-relaxed">
                        {item.text}
                    </p>
                </div>
            ))}
        </div>
    </div>
)

export default Highlight


// import { ArrowRightIcon, Award } from 'lucide-react'
// import React from 'react'

// const Highlight = () => {
//     return (
//         <div className="mt-8">
//             <div className="flex items-center gap-3 mb-6">
//                 <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//                     <Award size={16} className="text-light" />
//                 </div>
//                 <h2 className="text-2xl md:text-3xl font-bold text-brand">Highlights</h2>
//             </div>

//             <div className="space-y-2">
//                 <div className="flex items-start gap-3">
//                     <div className="w-6 h-6 mt-1 flex-shrink-0">
//                         <ArrowRightIcon size={24} className="text-gray-600" />
//                     </div>
//                     <p className="text-gray-700 text-lg leading-relaxed">
//                         Vast green valleys and forests of the <strong>Dhorpatan Hunting Reserve </strong> (1,325 sq km).
//                     </p>
//                 </div>

//                 <div className="flex items-start gap-3">
//                     <div className="w-6 h-6 mt-1 flex-shrink-0">
//                         <ArrowRightIcon size={24} className="text-gray-600" />
//                     </div>
//                     <p className="text-gray-700 text-lg leading-relaxed">
//                         <strong>Wildlife:</strong> Spot snow leopards, musk deer, blue sheep, and red panda.
//                     </p>
//                 </div>

//                 <div className="flex items-start gap-3">
//                     <div className="w-6 h-6 mt-1 flex-shrink-0">
//                         <ArrowRightIcon size={24} className="text-gray-600" />
//                     </div>
//                     <p className="text-gray-700 text-lg leading-relaxed">
//                         Authentic villages like <strong>Gurja Khani</strong> and <strong>Magar settlements</strong>.
//                     </p>
//                 </div>

//                 <div className="flex items-start gap-3">
//                     <div className="w-6 h-6 mt-1 flex-shrink-0">
//                         <ArrowRightIcon size={24} className="text-gray-600" />
//                     </div>
//                     <p className="text-gray-700 text-lg leading-relaxed">
//                         Thriving rhododendron and medicinal herb forests.
//                     </p>
//                 </div>

//                 <div className="flex items-start gap-3">
//                     <div className="w-6 h-6 mt-1 flex-shrink-0">
//                         <ArrowRightIcon size={24} className="text-gray-600" />
//                     </div>
//                     <p className="text-gray-700 text-lg leading-relaxed">
//                       <strong> Waterfalls, rivers, and close-up views </strong> of the Dhaulagiri & Annapurna ranges.
//                     </p>
//                 </div>

//                 <div className="flex items-start gap-3">
//                     <div className="w-6 h-6 mt-1 flex-shrink-0">
//                         <ArrowRightIcon size={24} className="text-gray-600" />
//                     </div>
//                     <p className="text-gray-700 text-lg leading-relaxed">
//                         Enduring local culture and sustainable rural life.
//                     </p>
//                 </div>
//             </div>
//         </div>
//     )
// }

// export default Highlight