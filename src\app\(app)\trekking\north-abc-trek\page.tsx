'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedAnnapurnaNorthBaseCampItinerary } from '@/data/trekking/north-abc/itinerary-detailed'
import { annapurnaNorthBaseCampSection } from '@/data/trekking/north-abc/package-info'
import { ScrollSpyTabs } from '@/components/common/sticky-scroll/sticky-scroll'

const NorthAnnapurnaBaseCampTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Narchyang – Annapurna North Base Camp – Ghasa",
        accommodation: "Hotels/Guesthouses (till Narchyang), Camping (beyond)",
        duration: "5–6 hrs daily on average",
        maxElevation: "4,310 m (Thulobugin Pass)",
        group: "2–6 max",
        region: "Annapurna Region",
        type: "Remote trails, muddy/rocky paths, camping sections",
        bestSeason: "March–June & September–November",
        grade: "Challenging (camping & remote terrain)"
    }

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Challenging trek with immense natural beauty of the Himalayas" },
        { text: "Breathtaking views of mighty mountains like Annapurna, Dhaulagiri and Machhapuchhre" },
        { text: "The trail is less crowded offering peaceful trekking experience" },
        { text: "Basic shelter are available in certain areas but this trail is famous for camping trek" },
        { text: "Along the trail, you may encounter wildlife such as musk deer, snow leopards, bears, wild dogs, and blue sheep, making the journey even more thrilling for nature lovers" },
        { text: "Pass through dense forest, waterfalls, lakes, caves and glaciers" },
    ]


    const content = {
        paragraphs: [
            `The Annapurna North Base Camp Trek, also known as the Maurice Herzog Trail, is a hidden gem in the Annapurna region. It offers a peaceful and less crowded alternative to the popular Annapurna Base Camp trek. Ideal for adventure seekers, this trail provides a unique and thrilling experience in a more untouched part of the Himalayas. The main highlights of this trek include stunning views of Annapurna, Machhapuchhre (Fishtail), and Dhaulagiri.

    The route was first explored in 2019 by the Annapurna Rural Municipality (ARM). Since then, efforts have been made by groups like the Trekking Agencies Association of Nepal (TAAN), Annapurna Conservation Area Project (ACAP), local communities, and the Ministry of Tourism to promote this historic trail to travel agencies and trekkers around the world.`
        ]
    }

    const note = {
        paragraphs: [
            `This is a remote camping trek with no teahouse infrastructure beyond Narchyang Village. You must be well-prepared for high altitudes and rugged terrain.`,
            `Wildlife encounters are possible along the way, so always follow your guide’s instructions and respect local conservation guidelines.`
        ]
    }

    const briefing = {
        paragraphs: [
            `We’ll conduct a pre-trip online briefing to discuss equipment, safety guidelines, route expectations, and camping logistics to ensure you're well-prepared before departure.`
        ]
    }

    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.",
                "Porter for each 2 person",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.",
                "Fresh/Dry fruits along the trek.",
                "Water bottle to store hot water overnight.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All the necessary paperwork and Annapurna conservation entry permit (ACAP permit & TIMS card etc.)",
            ],
        },
        {
            title: "Medical & Safety",
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                "First Aid Medical Kit Box with an oximeter.",
            ],
        },
        {
            title: "Camping & Equipment",
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                "Tents, cooking gear, and basic camping setup beyond Narchyang.",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Trek achievement certificate after the trek.",
                "Government taxes, TDS, VAT & other legal documents.",
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipment (sleeping bag, trekking shoes, headlamp, etc.)",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Porter",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];



    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,400 m)" },
        { day: 2, title: "Kathmandu → Pokhara (822 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 3, title: "Pokhara → Narchyang Village (1,530 m) [↑ 708 m, ~6 hrs drive]" },
        { day: 4, title: "Narchyang → Chhotepa (2,370 m) [↑ 840 m, ~5–6 hrs]" },
        { day: 5, title: "Chhotepa → Sandikharka (3,160 m) [↑ 790 m, ~5–6 hrs]" },
        { day: 6, title: "Sandikharka → Campsite (4,050 m) [↑ 890 m, ~4–5 hrs]" },
        { day: 7, title: "Campsite → Annapurna North BC (4,190 m) → Thulobugin Pass (4,310 m) [↑ 260 m, ~6 hrs]" },
        { day: 8, title: "Thulobugin → Ghasa (3,260 m) [↓ 1,050 m, ~6–7 hrs]" },
        { day: 9, title: "Ghasa → Pokhara (822 m) [↓ 2,438 m, ~6–7 hrs drive]" },
        { day: 10, title: "Pokhara → Kathmandu (1,400 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 11, title: "Final Departure from Kathmandu" },
    ]



    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Tsering Dolma",
            role: "Adventure Blogger",
            company: "Nomadic Footprints",
            rating: 5,
            content:
                "Absolutely breathtaking! The Annapurna North Base Camp was one of the most raw and beautiful places I’ve seen. The offbeat trail and camping experience made this trek feel like a true Himalayan expedition.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Alex Kumar",
            role: "Wildlife Photographer",
            company: "Untamed Lens",
            rating: 5,
            content:
                "As someone who loves nature, this trail was paradise. I got to photograph blue sheep and even saw signs of snow leopards. The silence and isolation made it surreal.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Nisha Adhikari",
            role: "Mountaineer",
            company: "Nepal Peak Club",
            rating: 4,
            content:
                "This trail is not for the faint-hearted but so worth it. The steep climbs, the wilderness, and the majestic view from base camp will stay with me forever.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Michael Tan",
            role: "Trekking Guide",
            company: "Explore Asia Treks",
            rating: 5,
            content:
                "Having guided across Nepal, this trek stands out for its solitude and rawness. My group loved the camping under the stars and glacier views. Highly recommend it!",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Sita Rana",
            role: "Nature Lover",
            company: "Himalayan Roots",
            rating: 5,
            content:
                "I joined this trek for a digital detox and found peace in every step. No crowds, just forests, rivers, and massive mountains. Felt connected with nature like never before.",
            avatar: "/images/review/5.png",
        },
    ];

    const sectionIds = [
        { id: "trek-info", label: "Trek Info" },
        { id: "highlights", label: "Highlights" },
        { id: "overview", label: "Overview" },
        { id: "gallery", label: "Gallery" },
        { id: "itinerary", label: "Itinerary" },
        { id: "map", label: "Map" },
        { id: "inclusions", label: "Inclusions/Exclusions" },
        { id: "gears", label: "Gears" },
        { id: "package-info", label: "Package Information" },
        { id: "reviews", label: "Reviews" },
    ]


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image4.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Annapurna North Base Camp Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">

                <ScrollSpyTabs sectionIds={sectionIds} />

                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <section id="trek-info" className="scroll-mt-[200px]">
                            <div>
                                <TrekInfo trekData={customTrekData} />
                            </div>

                            <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                            <div>
                                <div className="prose prose-lg max-w-none">
                                    <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                        <p className="text-justify">
                                            The Annapurna North Base Camp Trek, also known as the Maurice Herzog Trail, is a remote and challenging trek in the Annapurna region. It offers a peaceful and less-crowded alternative to the popular Annapurna Circuit. The trail takes you through pristine forests, alpine meadows, and rugged mountain passes, culminating at the Annapurna North Base Camp. This trek is best suited for experienced trekkers who are comfortable with high-altitude trekking and camping.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </section>


                        <section id="highlights" className="scroll-mt-[200px]">
                            <Highlight items={items} />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="overview" className="scroll-mt-[200px]">
                            <Overview
                                content={content}
                                note={note}
                                briefing={briefing}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                            <ItinerarySummary
                                title={`Pokhara Trail Running – ${shortItinerary.length} Days Short Itinerary`}
                                days={shortItinerary}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="gallery" className="scroll-mt-[200px]">
                            <GalleryWithMore items={galleryItems} />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="itinerary" className="scroll-mt-[200px]">
                            <ItineraryDetailed days={detailedAnnapurnaNorthBaseCampItinerary} />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="map" className="scroll-mt-[200px]">
                            <Mapandchart
                                imageSrc="/images/map/trekking/north-abc-trek.webp"
                                altText="Annapurna North Base Camp Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="inclusions" className="scroll-mt-[200px]">
                            <TrekInclusionsExclusions
                                inclusions={myInclusions}
                                exclusions={myExclusions}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="gears" className="scroll-mt-[200px]">
                            <Gears />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            {/* 
                            <PreperationandTraining />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}
                        </section>

                        <section id="package-info" className="scroll-mt-[200px]">
                            <PackageInformation sections={annapurnaNorthBaseCampSection} />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="reviews" className="scroll-mt-[200px]">
                            <TravelersReview
                                videos={sampleVideos}
                                onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                            />

                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            <StackedReviews
                                reviews={myReviews}
                                headerTitle="Customer Success Stories"
                            // headerIcon={SomeOtherIcon}
                            // containerHeightVH={80}
                            // stickyTopClass="top-32"
                            />
                        </section>
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default NorthAnnapurnaBaseCampTrek