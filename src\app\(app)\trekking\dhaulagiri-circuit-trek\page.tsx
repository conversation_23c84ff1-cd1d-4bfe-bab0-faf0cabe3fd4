'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedDhaulagiriItinerary } from '@/data/trekking/dhaulagiri-circuit/itinerary-detailed'
import { dhaulagiriSection } from '@/data/trekking/dhaulagiri-circuit/package-info'

const DhaulagiriCircuitTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Takam Village - Pokhara (Covers Dhaulagiri Circuit)",
        accommodation: "Tea Houses & Camping",
        duration: "5–6 hrs daily on average",
        maxElevation: "5,360 m (French Pass)",
        group: "2–6 max",
        region: "Dhaulagiri Region",
        type: "Unpaved, Muddy, Rocky and Glacier terrain",
        bestSeason: "March–June & September–November",
        grade: "Difficult – Extreme (Camping & Glacier Passes)"
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Trek to Dhaulagiri Base Camp at 4,750 meters with undefined routes to reach there every season due to glacier meltdown" },
        { text: "Cross French pass and Dhampus Pass, considered as one of the highest mountain passes" },
        { text: "Witness the extraordinary view of Dhaulagiri I, II, III, IV and Tukuche Peak along the trail" },
        { text: "Extremely remote and adventurous trekking experience" },
        { text: "Witness various wildlife like Himalayan Thar, Musk Deer, and Pheasants" },
        { text: "Meet local ethnic group of people and explore their traditional culture" },
        { text: "Beautiful Landscape, Glaciers, steep ridges and forest along the way" },
    ];



    const content = {
        paragraphs: [
            `The Dhaulagiri circuit trek is one of the most adventurous and exhilarating journey that takes you to the remote area of Dhaulagiri region with the view of Dhaulagiri I, Tukuche Peak and other Dhaulagiri peaks. This trek includes some of the highest mountain passes like French pass (5360 m), and Dhampus Pass (5240 m). Dhaulagiri circuit is extremely remote and one of the least explored trekking trails of Nepal.

            This trek is regarded as one of the most challenging routes in Nepal, ideal for experienced trekkers in search of extreme adventure in a remote and less-traveled area. The trail passes through a variety of landscapes, including dense rhododendron and oak forests, as well as rugged rocky areas. Diverse culture of people living in the village and various wildlife like Blue sheep, Red Panda, Himalayan Thar are also the key highlights of this trek.`
        ]
    }


    const note = {
        paragraphs: [
            `This trek involves multiple high-altitude crossings, including glacier paths and unstable terrain. Be well-prepared with mountaineering-grade equipment and follow your guide's instructions at all times.`,
            `There are limited teahouse facilities along the route. Camping is mandatory in several sections. You must be physically and mentally prepared for isolation and harsh conditions.`,
        ]
    }


    const briefing = {
        paragraphs: [
            `A pre-trek online briefing will be conducted covering packing essentials, emergency protocols, and high-altitude acclimatization techniques. You will also be introduced to your guide and porters, and given a chance to clarify any last-minute concerns about this extreme expedition.`,
        ]
    }


    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.",
                "Porter for each 2 person",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.",
                "Fresh/Dry fruits along the trek.",
                "Water bottle to store hot water overnight.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All the necessary paperwork and Annapurna conservation entry permit (ACAP permit & TIMS card etc.)",
            ],
        },
        {
            title: "Medical & Safety",
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                "First Aid Medical Kit Box with an oximeter.",
            ],
        },
        {
            title: "Camping & Equipment",
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                "Tents, cooking gear, and basic camping setup for remote sections of the trail.",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Trek achievement certificate after the trek.",
                "Government taxes, TDS, VAT & other legal documents.",
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipment (sleeping bag, trekking shoes, headlamp, crampons, etc.)",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Porter",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];




    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,400 m)" },
        { day: 2, title: "Kathmandu → Pokhara (822 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 3, title: "Pokhara → Takam Village (1,665 m) [↑ 843 m, ~120 km, 6–7 hrs]" },
        { day: 4, title: "Takam → Mudi (1,720 m) [↑ 55 m, 13 km, ~5–6 hrs]" },
        { day: 5, title: "Mudi → Bagar (2,080 m) [↑ 360 m, 10 km, ~5–6 hrs]" },
        { day: 6, title: "Bagar → Dovan Kharka (2,520 m) [↑ 440 m, ~8.5 km, ~5–6 hrs]" },
        { day: 7, title: "Dovan Kharka → Sallaghari (3,400 m approx) [↑ 880 m, 6 km, ~4 hrs]" },
        { day: 8, title: "Sallaghari → Italian Base Camp (3,660 m) [↑ 260 m, 4.2 km, ~4 hrs]" },
        { day: 9, title: "Acclimatization Day at Italian Base Camp (3,660 m)" },
        { day: 10, title: "Italian BC → Japanese BC (3,890 m) [↑ 230 m, ~4.6 km, ~5 hrs]" },
        { day: 11, title: "Japanese BC → Dhaulagiri Base Camp (4,748 m) [↑ 858 m, ~11 km, ~5–6 hrs]" },
        { day: 12, title: "Dhaulagiri BC → Hidden Valley (5,300 m approx) [↑ 552 m, ~8.7 km, ~6–7 hrs]" },
        { day: 13, title: "Hidden Valley → Yak Kharka (4,190 m) [↓ 1,110 m, ~12 km, ~6–7 hrs]" },
        { day: 14, title: "Yak Kharka → Pokhara (822 m) [↓ 3,368 m, ~8–10 hrs drive]" },
        { day: 15, title: "Pokhara → Kathmandu (1,400 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 16, title: "Final Departure from Kathmandu" },
    ];




    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Suman Rai",
            role: "High-Altitude Trekker",
            company: "Summit Seekers",
            rating: 5,
            content:
                "The Dhaulagiri Circuit was the most intense and rewarding trek I’ve ever done. Remote, raw, and absolutely stunning. The glacier crossings were no joke — highly recommend this to experienced trekkers only!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Leah Tanaka",
            role: "Backpacking Blogger",
            company: "Offbeat Trails",
            rating: 5,
            content:
                "If you’re looking for true wilderness, this is it. No crowds, just you and the mountains. French Pass blew my mind. I’ll never forget the isolation and beauty of Hidden Valley.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Kiran Shrestha",
            role: "Wildlife Enthusiast",
            company: "Nepal Nature Journal",
            rating: 4,
            content:
                "Spotted Himalayan Thar and a few pheasants deep in the forest. The ever-changing terrain and unpredictable weather made it both thrilling and tough.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Anna Müller",
            role: "Mountain Photographer",
            company: "Alpine Lens",
            rating: 5,
            content:
                "Photographing Dhaulagiri from the base camp was unreal. You need patience and stamina for this trek, but the light and landscapes are unmatched. Worth every step.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Ram Prasad",
            role: "Solo Trekker",
            company: "Freelancer",
            rating: 5,
            content:
                "This trek changed how I look at life. The trail challenges your body and mind — but what you find at 5,000 meters is peace, silence, and the real Himalaya.",
            avatar: "/images/review/5.png",
        },
    ];




    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Dhaulagiri Circuit Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Dhaulagiri Circuit Trek is an extremely challenging high-altitude expedition through one of Nepal’s most remote regions. It involves glacier crossings, steep passes, and days of full wilderness camping. Suitable only for physically fit and experienced trekkers, this journey rewards with unmatched Himalayan isolation, dramatic scenery, and raw adventure.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Dhaulagiri Circuit Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedDhaulagiriItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/map/trekking/dhaulagiri-circuit-trek.webp"
                            altText="Dhaulagiri Circuit Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={dhaulagiriSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default DhaulagiriCircuitTrek