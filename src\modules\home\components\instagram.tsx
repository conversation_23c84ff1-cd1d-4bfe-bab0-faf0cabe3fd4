import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Instagram as InstagramIcon, ExternalLink } from "lucide-react";
import Image from "next/image";

export default function Instagram() {
  const instagramPosts = [
    {
      image: "/images/fastpacking/hero/image1.webp",
      caption: "Beautiful mountain views from our latest trek"
    },
    {
      image: "/images/fastpacking/hero/image2.webp",
      caption: "Trekkers enjoying the journey"
    },
    {
      image: "/images/fastpacking/hero/image3.webp",
      caption: "Sunrise over the Himalayas"
    },
    {
      image: "/images/fastpacking/hero/image4.webp",
      caption: "Base camp achievement"
    },
    {
      image: "/images/fastpacking/hero/image5.webp",
      caption: "Mountain landscapes"
    },
    {
      image: "/images/fastpacking/hero/image6.webp",
      caption: "Adventure continues"
    }
  ];

  return (
    <section className=" px-4 mb-24 md:mb-16 bg-white">
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="md:text-4xl text-3xl font-bold text-dark mb-6">
            Find us on Instagram
          </h2>

          <div className="flex items-center justify-center gap-4 mb-8">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-pink-600 rounded-full flex items-center justify-center">
                <InstagramIcon className="w-6 h-6 text-white" />
              </div>
              <div className="text-left">
                <div className="font-semibold text-gray-900">Trek and Trail</div>
                <div className="text-sm text-gray-600">@trekandtrail</div>
              </div>
            </div>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white">
              Follow
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
          {instagramPosts.map((post, index) => (
            <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer group">
              <div className="relative h-48">
                <Image
                  src={post.image}
                  alt={post.caption}
                  fill
                  className="w-full h-32 md:h-40 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center">
                  <ExternalLink className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
