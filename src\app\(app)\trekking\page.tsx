"use client"

import PackageCard from '@/components/common/cards/card'
import { usePackages } from '@/modules/package/queries/get-package'
import Link from 'next/link'
import React from 'react'

const TrekkingPage = () => {
    const { packages, loading, error } = usePackages()

    if (loading) {
        return (
            <main className="container mx-auto px-4 py-12">
                <h1 className="text-3xl font-bold mb-8">Fast Packing</h1>
                <div className="text-center">Loading...</div>
            </main>
        )
    }

    if (error) {
        return (
            <main className="container mx-auto px-4 py-12">
                <h1 className="text-3xl font-bold mb-8">Fast Packing</h1>
                <div className="text-center text-red-500">{error}</div>
            </main>
        )
    }

    return (
        <main className="container mx-auto px-4 py-12">
            <h1 className="text-3xl font-bold mb-8">Trekking</h1>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {packages.map((pkg) => (
                    <Link href={`/trekking/${pkg.slug}`} key={pkg.id}>
                        <PackageCard package={pkg} />
                    </Link>
                ))}
            </div>
        </main>
    )
}

export default TrekkingPage

// import PackageCard from '@/components/common/cards/card'
// import { Package } from '@/types/package'
// import Link from 'next/link'
// import React from 'react'

// const trekkingPackage: Package[] = [
//   {
//     id: 1,
//     slug: "north-abc-trek",
//     title: "Annapurna North Base Camp Trek",
//     image: "/images/fastpacking/hero/image1.webp",
//     duration: "11 Days",
//     days: 11,
//     currency: "$",
//     originalPrice: 550,
//     currentPrice: 490,
//     priceUnit: "pp",
//     rating: 5,
//     difficulty: "Moderate",
//   },
//   {
//     id: 2,
//     slug: "dhaulagiri-circuit-trek",
//     title: "Dhaulagiri Circuit Trek",
//     image: "/images/fastpacking/hero/image2.webp",
//     duration: "16 Days",
//     days: 16,
//     currency: "$",
//     originalPrice: 650,
//     currentPrice: 490,
//     priceUnit: "pp",
//     rating: 5,
//     difficulty: "Hard",
//   },
//   {
//     id: 3,
//     slug: "dhorpatan-trek",
//     title: "Dhorpatan Trek",
//     image: "/images/fastpacking/hero/image3.webp",
//     duration: "11 Days",
//     days: 11,
//     currency: "$",
//     originalPrice: 500,
//     currentPrice: 490,
//     priceUnit: "pp",
//     rating: 4,
//     difficulty: "Hard",
//   },
//   {
//     id: 4,
//     slug: "khopra-ridge-trek",
//     title: "Khopra Ridge Trek with Hidden Lake",
//     image: "/images/fastpacking/hero/image4.webp",
//     duration: "10 Days",
//     days: 10,
//     currency: "$",
//     originalPrice: 480,
//     currentPrice: 435,
//     priceUnit: "pp",
//     rating: 4,
//     difficulty: "Moderate",
//   },
//   {
//     id: 5,
//     slug: "khumai-danda-trek",
//     title: "Khumai Danda Trek",
//     image: "/images/fastpacking/hero/image5.webp",
//     duration: "8 Days",
//     days: 8,
//     currency: "$",
//     originalPrice: 399,
//     currentPrice: 410,
//     priceUnit: "pp",
//     rating: 4,
//     difficulty: "Easy",
//   },
//   {
//     id: 6,
//     slug: "kori-trek",
//     title: "Kori Danda Trek",
//     image: "/images/fastpacking/hero/image6.webp",
//     duration: "8 Days",
//     days: 8,
//     currency: "$",
//     originalPrice: 420,
//     currentPrice: 390,
//     priceUnit: "pp",
//     rating: 4,
//     difficulty: "Moderate",
//   },
//   {
//     id: 7,
//     slug: "manaslu-circuit-trek",
//     title: "Manaslu Circuit Trek",
//     image: "/images/fastpacking/hero/image7.webp",
//     duration: "13 Days",
//     days: 13,
//     currency: "$",
//     originalPrice: 680,
//     currentPrice: 490,
//     priceUnit: "pp",
//     rating: 5,
//     difficulty: "Hard",
//   },
//   {
//     id: 8,
//     slug: "nar-phu-valley-trek",
//     title: "Nar Phu Valley Trek",
//     image: "/images/fastpacking/hero/image1.webp",
//     duration: "12 Days",
//     days: 12,
//     currency: "$",
//     originalPrice: 650,
//     currentPrice: 485,
//     priceUnit: "pp",
//     rating: 5,
//     difficulty: "Moderate",
//   },
//   {
//     id: 9,
//     slug: "three-passes-trek",
//     title: "Everest Three Passes Trek",
//     image: "/images/fastpacking/hero/image2.webp",
//     duration: "19 Days",
//     days: 19,
//     currency: "$",
//     originalPrice: 750,
//     currentPrice: 520,
//     priceUnit: "pp",
//     rating: 5,
//     difficulty: "Hard",
//   },
//   {
//     id: 10,
//     slug: "tsum-valley-trek",
//     title: "Tsum Valley Trek",
//     image: "/images/fastpacking/hero/image3.webp",
//     duration: "12 Days",
//     days: 12,
//     currency: "$",
//     originalPrice: 599,
//     currentPrice: 470,
//     priceUnit: "pp",
//     rating: 4,
//     difficulty: "Moderate",
//   },
// ]


// const TrekkingPage = () => {
//     return (
//         <main className="container mx-auto px-4 py-12">
//             <h1 className="text-3xl font-bold mb-8">Fast Packing</h1>
//             <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
//                 {trekkingPackage.map((pkg) => (
//                     <Link href={`/trekking/${pkg.slug}`} key={pkg.id}>
//                         <PackageCard key={pkg.id} package={pkg} />
//                     </Link>

//                 ))}
//             </div>
//         </main>
//     )
// }

// export default TrekkingPage
