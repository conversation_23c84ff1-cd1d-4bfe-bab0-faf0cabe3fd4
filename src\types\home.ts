export interface IHome {
  id: string;
  createdAt: string;
  updatedAt: string;
  adventure: IHomeAdventure;
  experience: IHomeExperience;
  hiking: IHomeHiking;
  review: IHomeReview;
  tailoredAdventure: IHomeTailoredAdventure;
  videoTestimonial: IHomeVideoTestimonial;
  hero: IHomeHero;
}

export interface IHomeAdventure {
  id: string;
  title: string;
  images: string[];
  points: string[];
  linkUrl: string;
  linkLabel: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeExperience {
  id: string;
  heading: string;
  subHeading: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  features: IHomeExperienceFeature[];
}

export interface IHomeExperienceFeature {
  id: string;
  homeExperienceId: string;
  title: string;
  subtitle: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeHiking {
  id: string;
  heading: string;
  subHeading: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  areas: IHomeHikingArea[];
}

export interface IHomeHikingArea {
  id: string;
  homeHikingId: string;
  image: string;
  title: string;
  subtitle: string;
  linkUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeReview {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  reviews: IHomeReviewItem[];
}

export interface IHomeReviewItem {
  id: string;
  homeReviewId: string;
  image: string;
  quote: string;
  name: string;
  designation: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeTailoredAdventure {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  features: IHomeTailoredAdventureFeature[];
}

export interface IHomeTailoredAdventureFeature {
  id: string;
  homeTailoredAdventureId: string;
  title: string;
  subtitle: string;
  linkLabel: string;
  linkUrl: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeVideoTestimonial {
  id: string;
  title: string;
  subtitle: string;
  homeId: string;
  createdAt: string;
  updatedAt: string;
  testimonials: IHomeVideoTestimonialItem[];
}

export interface IHomeVideoTestimonialItem {
  id: string;
  homeVideoTestimonialId: string;
  youtubeThumbnail: string;
  youtubeUrl: string;
  title: string;
  destination: string;
  date: string;
  createdAt: string;
  updatedAt: string;
}

export interface IHomeHero {
  id: string;
  videoUrl: string;
  titles: string[];
  subtitles: string[];
  images: string[];
  homeId: string;
  createdAt: string;
  updatedAt: string;
}
