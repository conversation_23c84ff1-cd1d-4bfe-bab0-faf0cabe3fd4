'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Divider from './divider';
import SearchBar from './search-bar';
import { IHomeHero } from '@/types/home';

const HeroSection = ({ hero }: { hero: IHomeHero }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % hero.images.length);
    }, 4000);

    return () => clearInterval(interval);
  }, [hero.images.length]);

  return (
    <div className="relative h-[75vh] md:h-screen min-h-[300px] overflow-hidden">
      <div className="hidden md:block absolute inset-0">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        >
          <source src={hero.videoUrl} type="video/mp4" />
        </video>
      </div>

      <div className="md:hidden absolute inset-0">
        <div className="relative w-full h-full">
          {hero.images.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-1000 ease-in-out ${index === currentSlide ? 'opacity-100' : 'opacity-0'
                }`}
            >
              <div className="relative w-full h-full">
                <Image
                  src={image}
                  alt={`Hero Image ${index + 1}`}
                  fill
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="absolute inset-0 bg-dark/10"></div>
      <Divider />

      <div className="relative z-10 flex h-full items-center justify-center">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h1
            className="text-5xl md:text-7xl lg:text-7xl font-medium text-light mb-4 font-serif italic"
            style={{
              textShadow: '3px 3px 6px rgba(94, 163, 0, 0.5)',
            }}
          >
            {hero.titles[0]}
          </h1>

          <p className="text-lg md:text-xl text-light/90 mb-8 max-w-2xl mx-auto">
            {hero.subtitles[0]}
          </p>
          <SearchBar />
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
