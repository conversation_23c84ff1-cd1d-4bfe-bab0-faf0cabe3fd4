import { useState, useEffect } from 'react'
import { Package } from '@/types/package'

interface ApiPackage {
  id: string
  name: string
  slug: string
  price: string
  discountPrice: string
  duration: string
  grade: string
  thumbnail: string
  mainImage: string
}

interface ApiResponse {
  success: boolean
  data: ApiPackage[]
}

export const usePackages = () => {
  const [packages, setPackages] = useState<Package[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await fetch('https://api.trailandtreknepal.com/package?sort=createdAt%3Adesc')
        const data: ApiResponse = await response.json()
        
        const transformedPackages = data.data.map((apiPackage, index) => ({
          id: index + 1,
          slug: apiPackage.slug,
          title: apiPackage.name,
          image: apiPackage.thumbnail || "/images/fastpacking/hero/image1.webp",
          duration: apiPackage.duration,
          days: parseInt(apiPackage.duration.match(/(\d+)/)?.[1] || '1'),
          currency: "$",
          originalPrice: parseInt(apiPackage.price),
          currentPrice: parseInt(apiPackage.discountPrice || apiPackage.price),
          priceUnit: "pp",
          rating: Math.floor(Math.random() * 2) + 4,
          difficulty: apiPackage.grade as 'Easy' | 'Moderate' | 'Hard',
        }))
        
        setPackages(transformedPackages)
      } catch (err) {
        setError('Failed to load packages')
      } finally {
        setLoading(false)
      }
    }

    fetchPackages()
  }, [])

  return { packages, loading, error }
}