'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedKhopraRidgeItinerary } from '@/data/trekking/khopra-ridge-trek/itinerary-detailed'
import { khopraRidgeSection } from '@/data/trekking/khopra-ridge-trek/package-info'

const KhopreRidgeTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Ghandruk - Pokhara",
        accommodation: "Tea Houses",
        duration: "Approx 6–7 hrs daily / Can vary",
        maxElevation: "Hidden Lake (4,250 m)",
        group: "2–10 max",
        region: "Annapurna Region",
        type: "Unpaved and Muddy",
        bestSeason: "March–May & September–November",
        grade: "Easy – Moderate",
    };



    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };


    const items: HighlightItem[] = [
        { text: "Amazing view of some of the highest mountains in the world from Khopra Ridge." },
        { text: "Explore the traditional villages of Ghandruk, Tadapani, Dobato, Pode Kharka, Jhinu Danda etc (learn about their unique culture)." },
        { text: "Rhododendron Blooms along the way during season" },
        { text: "Explore Hidden Lake" },
        { text: "Isolated trail in Annapurna Region with very few trekkers in the trail" },
    ];

    const content = {
        paragraphs: [
            `The Khopra Ridge Trek with Hidden Lake is a new trekking trail in the Annapurna Region that takes you to the least explored and less crowded into the hearts of pristine nature. Khopra Ridge and Hidden Lake are the hidden gems among the popular trekking routes that are yet to be explored by trekkers.

                Khopra Ridge trek offers the amazing view of mountains like Annapurna South, Dhaulagiri, Hiunchuli, Machhapuchhre, Lamjung Himal etc. You will cross various traditional villages in the outskirts of Pokhara valley to reach Khopra.

                Hidden Lake, also known as Tridevi Tal is located just below the Annapurna South Base Camp. The lake serves as its own water source and lacks external water supply. Set out on an adventure to Hidden Lake, where lush rhododendron forests, pristine wilderness, and uncharted trails offer a thrilling sense of discovery and fulfillment.`
        ]
    };

    const note = {
        paragraphs: [
            "The trail involves moderate uphill and downhill sections through forested paths, with one long day to Hidden Lake. A good fitness level and comfortable hiking shoes are recommended.",
            "While tea house accommodation is available throughout the route, facilities can be basic in remote areas. Weather conditions can change quickly in the highlands, so pack layers and waterproof gear."
        ]
    };

    const briefing = {
        paragraphs: [
            "Before the trek, we provide a detailed online briefing covering packing checklists, altitude tips, route orientation, and safety guidance. You’ll also get a chance to meet your guide virtually and ask any questions regarding the trek logistics and trail expectations."
        ]
    };




    const myInclusions: Category[] = [
        {
            title: "Meals & Water",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 Meals per day which includes Breakfast, Lunch and Dinner.",
                "Medicines and water purifiers will be provided.",
            ],
        },
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional Guide and Porters, their food, accommodation and insurance etc.",
            ],
        },
        {
            title: "Permits & Documentation",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "Permits and any other necessary documents.",
            ],
        },
        {
            title: "Taxes & Charges",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government and service tax.",
            ],
        },
    ];



    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipment (e.g. trekking boots, rain jacket, sleeping bag, etc.).",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Porters.",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e. illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other Exclusions",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];






    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]


    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,400 m)" },
        { day: 2, title: "Kathmandu → Pokhara (822 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 3, title: "Pokhara → Ghandruk (1,940 m) drive, trek to Tadapani (2,630 m) [↑ 690 m, ~6.5 km, 4–5 hrs]" },
        { day: 4, title: "Tadapani → Dobato (3,350 m) [↑ 720 m, ~6.5 km, ~5 hrs]" },
        { day: 5, title: "Dobato → Chistibung (3,000 m) [↓ 350 m, ~7 km, ~5 hrs]" },
        { day: 6, title: "Chistibung → Khopra Ridge (3,640 m), explore, descend to Pode Kharka (3,650 m) [↑ 650 m, ~6–7 hrs]" },
        { day: 7, title: "Pode Kharka → Hidden Lake (4,250 m), trek to Tiribung (3,430 m) [↓ 820 m, ~26 km approx, 7–8 hrs]" },
        { day: 8, title: "Tiribung → Jhinu Danda (1,780 m), drive to Pokhara (822 m) [↓ 1,650 m, ~7–8 hrs]" },
        { day: 9, title: "Pokhara → Kathmandu (1,400 m) [200 km, ~6–7 hrs drive / 25 min flight]" },
        { day: 10, title: "Final Departure from Kathmandu" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Sophie Müller",
            role: "Trekker & Nature Enthusiast",
            company: "Alpine Echoes",
            rating: 5,
            content:
                "Khopra Ridge took my breath away — both literally and visually! The views of Dhaulagiri and Annapurna South were absolutely surreal. Hidden Lake was a spiritual highlight.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Rabin Thapa",
            role: "Travel Vlogger",
            company: "WanderNepal",
            rating: 4,
            content:
                "This trail is perfect for those who want isolation without going extreme. I loved the rhododendron forests and how uncrowded it felt. The local hospitality was amazing.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Emily Chen",
            role: "Wildlife Photographer",
            company: "Trail Lens",
            rating: 5,
            content:
                "From sunrise at Mulde View Point to reflections at Hidden Lake — every frame was gold. I’m grateful for the team’s support through the steep ridges and long days.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Pasang Gurung",
            role: "Mountain Guide (Retired)",
            company: "N/A",
            rating: 5,
            content:
                "Having led treks in this region before, I took this one as a guest. It still surprised me! Khopra is a hidden gem, with well-balanced terrain and unmatched views.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Jack Patel",
            role: "Adventure Seeker",
            company: "Backpack Horizons",
            rating: 4,
            content:
                "Challenging but totally worth it. Especially the off-beat stretch to Hidden Lake. This trail is underrated and deserves way more attention.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Khopra Ridge Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Khopra Ridge Trek is a peaceful yet scenic adventure in the Annapurna region, offering panoramic views of Dhaulagiri, Annapurna South, and Hidden Lake. With fewer trekkers and authentic village trails, this route is perfect for those seeking a less-crowded Himalayan experience.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Khopra Ridge Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedKhopraRidgeItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/map/trekking/khopra-ridge-trek.webp"
                            altText="Khopra Ridge Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={khopraRidgeSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default KhopreRidgeTrek