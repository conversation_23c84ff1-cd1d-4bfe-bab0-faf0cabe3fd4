'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedAnnapurnaFoothillsProItinerary } from '@/data/trailrunning/annapurna-circuit/pro-foothill-itinerary-detailes'
import { annapurnaFoothillsProSection } from '@/data/trailrunning/annapurna-circuit/pro-foothill-package'

const AnnapurnaFootHillsProRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Hile - Pokhara',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 6–7 hrs daily / Can vary',
        maxElevation: '4,200 m',
        group: '2–10 max',
        region: 'Annapurna Foothills, Pokhara Region',
        type: 'Mixture of Paved and Unpaved paths',
        bestSeason: 'March–May & September–November',
        grade: 'Moderate (can be difficult)',
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Breathtaking view of Annapurna Mountain (8091 m), Machhapuchhre Mountain (6993 m).' },
        { text: 'Rhododendron blooms during the season.' },
        { text: 'Enjoy the hot spring at Jhinu Danda to recover from fatigue.' },
        { text: 'Lwang village is very famous for its tea production and traditional culture.' },
        { text: 'Extraordinary landscapes from Khumai Danda and Mohare Danda.' },
        { text: 'Traditional villages with rich cultural history.' },
    ];

    const content = {
        paragraphs: [
            `The outskirts of Pokhara valley offers various trails famous for trekking and running. This 14 days trail running program carries you to amazing places around Pokhara valley with breathtaking mountain views and landscapes. The 14 days program is moderate - difficult yet one of the best trail running experiences for professional runners. The closeup view of Annapurna, Mardi and Machhapuchhre Mountain are the key sightings of this program.

    The trail begins from Hile and ends at Pokhara, passing through iconic locations such as Mohare Danda, Jhinu Danda, Mardi Base Camp, Khumai Danda, and Lwang. This trail running adventure offers a thrilling and unforgettable experience for professional runners, combining challenging terrain with breathtaking views throughout the journey.

    All runners will cover approx 15 to 20 kilometers each day, running for about 3 to 4 hours. They will encounter a variety of surfaces, including paved roads, unpaved paths, and muddy terrain. Since muddy areas tend to be slippery, it's important to wear appropriate footwear for better grip and safety.`,
        ],
    };


    const note = {
        paragraphs: [
            `This trail running program involves multiple uphill and downhill segments through forest trails and high-altitude terrain, reaching up to 4,200 meters. It's best suited for those with prior trail running or trekking experience.`,

            `Please arrive in Pokhara at least a day prior to the trip start for gear checks and orientation. Terrain may vary day-to-day, so good footwear and physical preparation are essential.`,
        ],
    };


    const briefing = {
        paragraphs: [
            `Once you confirm your booking, our team will schedule an online pre-departure briefing to walk you through the itinerary, gear checklist, emergency protocols, and physical preparation tips. Feel free to ask questions during the session.`,
        ],
    };

    const myInclusions: Category[] = [
        {
            title: 'Guides',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, including their food, accommodation, salary, equipment, and insurance.',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                '3 meals per day (Breakfast, Lunch, and Dinner).',
                'Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
            ],
        },
        {
            title: 'Accommodation',
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                'Lodge and tea house accommodation throughout the trail.',
            ],
        },
        {
            title: 'Porters',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                'Porter for every 2 participants to carry gear.',
                'Duffel bag provided for porter loads.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All necessary permits: ACAP, TIMS card, and other paperwork as required.',
            ],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with oximeter.',
                'Trek achievement certificate after the trek.',
                'Government taxes, VAT, TDS & other legal documents.',
                'Water bottle to store hot water overnight.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Additional Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any food or drink items beyond the provided 3 meals/day and hot beverages.',
            ],
        },
        {
            title: 'Personal Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Personal running gear, shoes, and accessories.'],
        },
        {
            title: 'Tips & Gratitude',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: ['Tips for the Guide and Porter (optional but appreciated).'],
        },
        {
            title: 'Unexpected Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Costs arising from illness, injury, delays, or natural disasters.',
            ],
        },
        {
            title: 'Other Expenses',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: ['Anything not mentioned in the “What’s Included” section.'],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Trek Hile → Mohare Danda (3300 m), 9.3 km, approx 2–3 hrs [↑ 1760 m]" },
        { day: 2, title: "Trek Mohare Danda → Dobato (3350 m), 9.6 km, approx 3 hrs [↑ 50 m]" },
        { day: 3, title: "Trek Dobato → Jhinu Danda (1780 m), 16.5 km, approx 4–5 hrs [↓ 1570 m]" },
        { day: 4, title: "Trek Jhinu Danda → Mardi Low Camp (2970 m), 24.5 km, approx 6 hrs [↑ 1190 m]" },
        { day: 5, title: "Trek Low Camp → Mardi Base Camp Viewpoint (4500 m) → High Camp (3550 m), 15 km, approx 5–6 hrs [↑ 1530 m / ↓ 580 m]" },
        { day: 6, title: "Trek High Camp → Sidhing Village (1945 m), 8.2 km, approx 3 hrs [↓ 1605 m]" },
        { day: 7, title: "Rest Day at Sidhing Village" },
        { day: 8, title: "Trek Sidhing → Korchon Hill (3682 m), 11 km, approx 4 hrs [↑ 1737 m]" },
        { day: 9, title: "Trek Korchon → Sirka View Point → Khumai Danda (3245 m), 6.75 km, approx 2–2.5 hrs [↓ 437 m]" },
        { day: 10, title: "Trek Khumai Danda → Ghachowk (1240 m), 15 km, approx 3–4 hrs [↓ 2005 m]" },
        { day: 11, title: "Trek Ghachowk → Lwang (1550 m), ~19 km, approx 4–5 hrs [↑ 310 m]" },
        { day: 12, title: "Trek Lwang → Bhadaure (1400 m), 14.5 km, approx 3–4 hrs [↓ 150 m]" },
        { day: 13, title: "Trek Bhadaure → Jhuma Danda (1580 m), 17.5 km, approx 4–5 hrs [↑ 180 m]" },
        { day: 14, title: "Trek Jhuma Danda → Pokhara (822 m), 16 km, approx 3–4 hrs [↓ 758 m]" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Sophie Williams",
            role: "Trail Running Coach",
            company: "EnduroRun UK",
            rating: 5,
            content:
                "This was hands-down the most scenic trail I’ve ever run. Every day felt like a new challenge with unreal views of the Himalayas. Well-organized and safe throughout!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Ankit Gurung",
            role: "Ultrarunner",
            company: "Nepal Trail Runners",
            rating: 5,
            content:
                "The Annapurna foothills are magical. This route had it all—jungle trails, ridges, tea villages, and even hot springs! Huge respect for the crew managing the logistics.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Maya Chen",
            role: "Adventure Filmmaker",
            company: "RunWild Media",
            rating: 4,
            content:
                "Great mix of nature and culture. The section from Mardi Base Camp to Sidhing was tough but unforgettable. Would love slightly better WiFi in some places, but hey—it’s the mountains!",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Dawa Tamang",
            role: "Mountain Guide",
            company: "Himalaya Active",
            rating: 5,
            content:
                "I’ve guided in this area for years. Seeing it through a runner’s perspective was something new. Loved the pacing and support. Top-notch coordination and route choice!",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Elena Rossi",
            role: "Marathoner",
            company: "Milano FastFeet",
            rating: 5,
            content:
                "14 days of stunning madness! Steep climbs, forest descents, and friendly villages along the way. My legs are sore but my heart is full.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Pokhara Trail: 14 Days of Challenge and Untamed Beauty"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is moderate level with decent uphill climb and has to cover long distances during the program. As you will cross the altitude of over 5000+ meters it is important that you are physically fit for this trail running program. This program is recommended for the person who has certain trekking/running experience over 4000+ m. The accommodations in this area are decent - remote with increase in altitude.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Annapurna Foothills Pro Running – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedAnnapurnaFoothillsProItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Annapurna Foothills Pro Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining /> */}
                        {/* <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={annapurnaFoothillsProSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default AnnapurnaFootHillsProRunning