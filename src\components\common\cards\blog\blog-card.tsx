// components/BlogCard.tsx
import React from 'react';
import { ArrowRight } from 'lucide-react';
import Image from 'next/image';
import { BlogPost } from '@/data/blog';
import Link from 'next/link';

interface BlogCardProps {
  blog: BlogPost;
}

const BlogCard: React.FC<BlogCardProps> = ({ blog }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-300 group">
      <div className="relative h-48 overflow-hidden">
        <Image
          src={blog.image}
          alt={blog.title}
          fill
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="absolute top-3 left-3">
          <span className="bg-brand text-white text-xs font-medium px-2 py-1 rounded-full">
            {blog.category}
          </span>
        </div>
      </div>

      <div className="p-4">
        <div className="mb-3">
          <h3 className="text-lg font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-brand transition-colors">
            {blog.title}
          </h3>
          <p className="text-sm text-gray-600 leading-relaxed line-clamp-3">
            {blog.excerpt}
          </p>
        </div>

        <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
          <span>{blog.readTime}</span>
          <span>{new Date(blog.publishedDate).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
          })}</span>
        </div>

        <Link
          href={`/blogs/${blog.slug}`}
          className="flex items-center gap-2 text-brand font-medium text-sm hover:gap-3 transition-all duration-200 group"
        >
          Read More
          <ArrowRight
            size={16}
            className="group-hover:translate-x-1 transition-transform"
          />
        </Link>
      </div>
    </div>
  );
};

export default BlogCard;