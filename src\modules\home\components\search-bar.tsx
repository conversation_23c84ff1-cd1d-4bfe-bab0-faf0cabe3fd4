import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Search } from 'lucide-react'
import React, { useState } from 'react'

const SearchBar = () => {
    const [searchQuery, setSearchQuery] = useState("")

    return (
        <div>
            <div className="max-w-2xl mx-auto">
                <div className="relative">
                    <Input
                        type="text"
                        placeholder="Search Trips"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full h-14 pl-6 pr-14 text-lg bg-light/95 backdrop-blur-sm border-0 rounded-full shadow-lg placeholder:text-gray-500 focus:ring-2 focus:ring-brand focus:ring-offset-0"
                    />
                    <Button
                        size="icon"
                        className="absolute right-2 top-2 h-10 w-10 rounded-full bg-brand hover:bg-brand/80 text-light"
                    >
                        <Search className="h-5 w-5" />
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default SearchBar