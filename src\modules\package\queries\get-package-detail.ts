import { useState, useEffect } from 'react'

export const usePackageDetail = (slug: string) => {
  const [packageDetail, setPackageDetail] = useState<PackageDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!slug) return

    const fetchPackageDetail = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch(`https://api.trailandtreknepal.com/package/slug/${slug}`)
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data: ApiResponse = await response.json()
        
        if (!data.success) {
          throw new Error(data.message || 'Failed to fetch package details')
        }
        
        setPackageDetail(data.data)
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred'
        setError(errorMessage)
        console.error('Error fetching package detail:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPackageDetail()
  }, [slug])

  return { packageDetail, loading, error }
}
