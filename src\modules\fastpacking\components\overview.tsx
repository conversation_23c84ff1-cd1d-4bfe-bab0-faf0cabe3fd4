import { Search, FileText, Calendar } from 'lucide-react'
import React from 'react'

interface SectionBlock {
  paragraphs: string[]

}

export interface OverviewProps {
  content: SectionBlock
  note: SectionBlock
  briefing: SectionBlock
}

const Overview: React.FC<OverviewProps> = ({
  content,
  note,
  briefing,
}) => {
  const paras = content.paragraphs[0]
    .split(/\r?\n\r?\n/)
    .map(p => p.trim())
    .filter(p => p.length > 0)

  return (
    <div>
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
          <Search size={16} className="text-light" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-brand">Overview</h2>
      </div>

      <div className="prose prose-lg max-w-none mb-4">
        <div className="text-gray-700 text-lg leading-relaxed space-y-4">
          {paras.map((p, i) => (
            <p className="text-justify" key={i}>{p}</p>
          ))}
        </div>
      </div>

      {/* Note & Briefing */}
      <div className="space-y-6 mb-8">
        {/* Note */}
        <div className={`${'bg-brand/10'} rounded-lg p-6`}>
          <div className="flex items-start gap-3 mb-2">
            <FileText
              className="w-6 h-6 text-brand flex-shrink-0"
              size={24}
            />
            <h3 className="text-lg font-semibold text-gray-800">Note</h3>
          </div>
          {note.paragraphs.map((p, i) => (
            <p
              className={`text-gray-700${i > 0 ? ' mt-4' : ''}`}
              key={i}
            >
              {p}
            </p>
          ))}
        </div>

        <div className="bg-secondary/20 rounded-lg p-6">
          <div className="flex items-start gap-3 mb-2">
            <Calendar
              className="w-6 h-6 text-brand flex-shrink-0"
              size={24}
            />
            <h3 className="text-lg font-semibold text-gray-800">
              Online Trip Briefing
            </h3>
          </div>
          {briefing.paragraphs.map((p, i) => (
            <p
              className="text-gray-700"
              key={i}
            >
              {p}
            </p>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Overview


// import { Calendar, FileText, Search } from 'lucide-react'
// import React from 'react'

// const Overview = () => {
//     return (
//         <div>
//             <div className="flex items-center gap-3 mb-6">
//                 <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
//                     <Search size={16} className="text-light" />
//                 </div>
//                 <h2 className="text-2xl md:text-3xl font-bold text-brand">Overview</h2>
//             </div>

//             <div className="prose prose-lg max-w-none mb-4">
//                 <div className="text-gray-700 text-lg leading-relaxed space-y-4">
//                     <p className="text-justify">
//                         The Dhorpatan Trek takes you deep into west Nepal’s untouched wilderness, traversing the only hunting reserve in the country. Dhorpatan, sprawling across parts of Baglung, Rukum, and Myagdi, is a marvel of hidden Himalayan valleys, wildlife-rich forests, and vibrant ethnic communities. This journey is not just about scenery—it’s a rare window into the old rhythms of Himalayan life, from sustainable herding, farming, and herbal medicine, to living traditions untouched by time. Expect plentiful wildlife, mountain vistas, and some of Nepal’s friendliest hospitality on a rewarding trail few visitors have explored.
//                     </p>
//                 </div>
//             </div>

//             <div className="space-y-6 mb-8">
//                 <div className="bg-brand/10 rounded-lg p-6">
//                     <div className="flex items-start gap-3 mb-2">
//                         <FileText className="w-6 h-6 text-brand flex-shrink-0" />
//                         <h3 className="text-lg font-semibold text-gray-800">Note</h3>
//                     </div>
//                     <p className="text-gray-700">
//                         Dhorpatan Trek starts with a 6–7 hours bus ride from Kathmandu to Pokhara (approx. 200 km),
//                         <strong> but you can fly from Kathmandu to Pokhara for an extra cost.</strong> Please note that the
//                         itinerary below is a general guideline that might need slight alterations according to circumstances.
//                     </p>
//                     <p className="text-gray-700 mt-4">
//                         To stay on track with your adventure, we advise arriving in Kathmandu at least one day before the
//                         trek, by 3 p.m., to allow time for last-minute needs.
//                     </p>
//                 </div>

//                 <div className="bg-secondary/20 rounded-lg p-6">
//                     <div className="flex items-start gap-3 mb-2">
//                         <Calendar className="w-6 h-6 text-brand flex-shrink-0" />
//                         <h3 className="text-lg font-semibold text-gray-800">Online Trip Briefing</h3>
//                     </div>
//                     <p className="text-gray-700">
//                         After confirming your booking and receiving your essential documents, we&apos;ll invite you to an online trip
//                         briefing. This call will cover the full itinerary, what to pack, and what to expect during the trek. You&apos;ll
//                         get the briefing schedule by email.
//                     </p>
//                 </div>
//             </div>

//         </div>
//     )
// }

// export default Overview