import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Building, Facebook, Instagram, Linkedin, Mail, MapPin, Phone, Twitter } from 'lucide-react'
import Link from 'next/link'
import React from 'react'

const ContactInformation = () => {
    return (
        <div className="space-y-6">
            <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                    <CardTitle className="text-2xl">Contact Information</CardTitle>
                    <CardDescription>
                        Reach out to us through any of these channels.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                    <div className="flex items-start space-x-4">
                        <div className="bg-brand/10 p-3 rounded-full">
                            <Mail className="h-5 w-5 text-brand" />
                        </div>
                        <div>
                            <h3 className="font-medium">Email</h3>
                            <Link
                                target="_blank"
                                href="mailto:<EMAIL>"
                                className="text-brand hover:underline"
                            >
                                <EMAIL>
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-start space-x-4">
                        <div className="bg-brand/10 p-3 rounded-full">
                            <Phone className="h-5 w-5 text-brand" />
                        </div>
                        <div>
                            <h3 className="font-medium">Phone</h3>
                            <Link
                                href={"tel:+9779800000000"}
                                className="text-brand hover:underline"
                            >
                                +977 9800000000
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-start space-x-4">
                        <div className="bg-brand/10 p-3 rounded-full">
                            <MapPin className="h-5 w-5 text-brand" />
                        </div>
                        <div>
                            <h3 className="font-medium">Office Location</h3>
                            <Link
                                target="_blank"
                                href={"https://maps.app.goo.gl/i6b7GDzwqHBTNL5k8"}
                                className="text-brand hover:underline"
                            >
                                Lakeside, Nepal
                            </Link>
                        </div>
                    </div>

                    <div className="flex items-start space-x-4">
                        <div className="bg-brand/10 p-3 rounded-full">
                            <Building className="h-5 w-5 text-brand" />
                        </div>
                        <div>
                            <h3 className="font-medium">Office Hours</h3>
                            <p className="mt-2 text-[#444] text-sm font-medium">
                                Sunday - Friday: 9 AM-8:30 PM
                            </p>
                            <p className="mt-1 text-[#444] text-sm font-medium">
                                Saturday, 10 AM-8:30 PM
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
                <CardHeader className="pb-4">
                    <CardTitle className="text-2xl">Connect With Us</CardTitle>
                    <CardDescription>
                        Follow us on social media for recent updates.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="flex space-x-4 justify-center">
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-brand/10 p-3 rounded-full hover:bg-brand/20 transition-colors"
                        >
                            <Facebook className="h-6 w-6 text-brand" />
                            <span className="sr-only">Facebook</span>
                        </Link>
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-brand/10 p-3 rounded-full hover:bg-brand/20 transition-colors"
                        >
                            <Twitter className="h-6 w-6 text-brand" />
                            <span className="sr-only">Twitter</span>
                        </Link>
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-brand/10 p-3 rounded-full hover:bg-brand/20 transition-colors"
                        >
                            <Instagram className="h-6 w-6 text-brand" />
                            <span className="sr-only">Instagram</span>
                        </Link>
                        <Link
                            href="#"
                            target="_blank"
                            className="bg-brand/10 p-3 rounded-full hover:bg-brand/20 transition-colors"
                        >
                            <Linkedin className="h-6 w-6 text-brand" />
                            <span className="sr-only">LinkedIn</span>
                        </Link>
                    </div>
                </CardContent>
            </Card>
        </div>
    )
}

export default ContactInformation