import React from 'react';
import HeroSection from '../components/hero-section';
import { TailoredEveryAdventure } from '../components/tailored-every-adventure';
import Instagram from '../components/instagram';
import TestimonialsSection from '../components/testimonials';
import ExperienceSection from '../components/experience-section';
import ActivityCategories from '../components/category-section';
import HikingAreasSection from '../components/hiking-areas';
import BlogsSection from '../components/blogs-section';
import VideoTestimonial from '../components/video.testimonial';
import FeaturedPackages from '../components/feature-section';
import UpcomingTreksSection from '../components/upcoming-trek';
import { IHome } from '@/types/home';

const HomeTemplate = ({ home }: { home: IHome }) => {
  return (
    <div>
      <HeroSection hero={home.hero} />
      <ActivityCategories adventure={home.adventure} />
      <ExperienceSection experience={home.experience} />
      <HikingAreasSection hiking={home.hiking} />
      <FeaturedPackages />
      <UpcomingTreksSection />
      <TailoredEveryAdventure adventure={home.tailoredAdventure} />
      <BlogsSection />
      <Instagram />
      <VideoTestimonial testimonial={home.videoTestimonial} />
      <TestimonialsSection testimonial={home.review} />
    </div>
  );
};

export default HomeTemplate;
