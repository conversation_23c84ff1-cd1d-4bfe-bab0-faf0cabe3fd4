'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { hemjaTatopaniSection } from '@/data/trailrunning/hemja-totopani/package-info'
import { detailedHemjaTatopaniItinerary } from '@/data/trailrunning/hemja-totopani/itinerary-detailed'

const HemjaTatopaniTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Hemja - Tatopani",
        accommodation: "Lodge and Tea Houses",
        duration: "Approx 6-7 hrs daily/Can vary",
        maxElevation: "2,874 m (Ghorepani)",
        group: "2–10 max",
        region: "Annapurna Region",
        type: "Mixture of Paved and Unpaved paths",
        bestSeason: "March to May and September to November",
        grade: "Easy to Moderate",
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "View of Fishtail, Annapurna and Dhaulagiri Mountains." },
        { text: "Local wildlife and lush vegetation along the trail." },
        { text: "Stunning landscapes and diverse natural resources throughout the journey." },
        { text: "Rhododendron blooms during the flowering season." },
        { text: "Explore culturally rich villages with historic traditions." },
    ];

    const content = {
        paragraphs: [
            `The trail running program from Hemja - Tatopani will take you through muddy, unpaved roads and steep trails to test your physical fitness. This trail will cover some of the best trekking areas around Pokhara valley. The amazing view of Fishtail, Annapurna and Dhaulagiri etc and amazing landscapes and natural resources along the way makes this journey exciting.

    This trail run takes you through stunning locations like Pitam Deurali, Jhinu Hot Spring, Tadapani, Ghorepani, and Tatopani. Day 2 offers an exciting break, as runners can relax and soothe their tired muscles in the natural hot spring after the efforts of Day 1. Along the way, you'll pass through traditional villages around the Pokhara Valley, encounter local wildlife and lush vegetation, and witness the vibrant rhododendron blooms along the Ghorepani trail—making this an unforgettable experience.`,
        ],
    };


    const note = {
        paragraphs: [
            `This program starts at Hemja and ends at Tatopani. We recommend arriving in Pokhara at least a day early to settle in and prep for the trail.`,
            `Although the trail is graded Easy to Moderate, it involves daily runs of 15–22 km and requires basic physical fitness.`,
        ],
    };


    const briefing = {
        paragraphs: [
            `Once your booking is confirmed, we’ll organize a virtual or in-person briefing covering the day-wise itinerary, gear checklist, and safety measures for the trail.`,
        ],
    };



    const myInclusions: Category[] = [
        {
            title: "Guides",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.",
            ],
        },
        {
            title: "Food & Drink",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.",
                "Fresh/Dry fruits along the trek.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All the necessary paperwork and Annapurna conservation entry permit (ACAP permit & TIMS card etc.)",
            ],
        },
        {
            title: "Porters",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Porter for each 2 person",
            ],
        },
        {
            title: "Medical & Extras",
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                "First Aid Medical Kit Box with an oximeter.",
                "Trek achievement certificate after the trek.",
                "Water bottle to store hot water overnight.",
                "Government taxes, TDS, VAT & other legal documents.",
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: "Additional Food/Drinks",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Personal Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ["Personal equipments"],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: ["Tips for the Guide and Potter"],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other Expenses",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: ["Any expenses that are not in the included section."],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        {
            day: 1,
            title: "Run Hemja → Pitam Deurali (2,100 m), 14.6 km, approx 4 hrs [↑ 1,003 m]",
        },
        {
            day: 2,
            title: "Run Pitam Deurali → Jhinu Danda (1,780 m), 17.8 km, approx 5 hrs [↓ 320 m]",
        },
        {
            day: 3,
            title: "Run Jhinu Danda → Tadapani (2,610 m), 11.5 km, approx 3–4 hrs [↑ 830 m]",
        },
        {
            day: 4,
            title: "Run Tadapani → Ghorepani via Mulde (2,874 m), 14 km, approx 4 hrs [↑ 264 m]",
        },
        {
            day: 5,
            title: "Run Ghorepani → Tatopani (1,190 m), 21.4 km, approx 5–6 hrs [↓ 1,684 m]",
        },
        {
            day: 6,
            title: "Drive Tatopani → Pokhara (822 m), approx 3 hrs [↓ 368 m]",
        },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Elina Gurung",
            role: "Recreational Runner",
            company: "Pokhara Trail Club",
            rating: 5,
            content:
                "Such a rejuvenating experience! The natural hot springs in Jhinu and Tatopani were the highlight after long days of trail running. Perfect for beginners looking to step into multi-day runs.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "James Doyle",
            role: "Fitness Coach",
            company: "Trail Ready UK",
            rating: 5,
            content:
                "Loved the balance of easy and moderate terrain. The landscape kept changing—lush forests, local villages, and incredible mountain views. Well-organized and beginner-friendly!",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Tsering Lama",
            role: "Himalayan Explorer",
            company: "Nomad Trails",
            rating: 4,
            content:
                "Great mix of comfort and challenge. Tadapani and Ghorepani were truly scenic. The route was smooth overall, though some muddy sections were tricky during light rain.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Priya Shrestha",
            role: "Outdoor Enthusiast",
            company: "Eco Run Nepal",
            rating: 5,
            content:
                "Running through rhododendron forests with views of Annapurna and Machhapuchhre—absolutely magical! I’d do this route again in a heartbeat.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Alex Romero",
            role: "Wellness Blogger",
            company: "Run & Reset",
            rating: 5,
            content:
                "This 6-day trail was just right. Not too intense, but still rewarding. Loved the hot springs and peaceful villages. Great introduction to trail running in Nepal.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image6.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Hemja - Tatopani Trail Running |  6 days"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program is easy, small steep Ascent/Descent and can be completed by someone with little running experience. The accommodations at the villages we stay at are very decent with facilities available.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Hemja to Tatopani – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedHemjaTatopaniItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Hemja to Tatopani Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={hemjaTatopaniSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default HemjaTatopaniTrailRunning