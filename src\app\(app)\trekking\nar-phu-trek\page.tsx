'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import Gallery<PERSON>ithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedNarPhuItinerary } from '@/data/trekking/nar-phu-trek/detailed-itinerary'
import { narPhuSection } from '@/data/trekking/nar-phu-trek/package-info'

const NarPhuTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Kathmandu – Nar Phu Valley – Manang",
        accommodation: "Tea Houses",
        duration: "Approx 5–7 hrs daily/Can vary",
        maxElevation: "Kang La Pass (5,320 m)",
        group: "2–10 max",
        region: "Annapurna Region",
        type: "Unpaved and rocky trails",
        bestSeason: "March–May & September–November",
        grade: "Moderate – Challenging",
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Explore the Buddhist and Tibetan culture of the region" },
        { text: "Reach at one of the highest mountain passes of Nepal Kang La Pass at 5,320 m." },
        { text: "The amazing landscape and exploration of diverse culture makes this journey more exciting" },
        { text: "Visit Nar Gompa and Phu Gompa in Nar Phu village" },
        { text: "Remote and Least Crowded unlike other trails" },
    ];


    const content = {
        paragraphs: [
            `The Nar Phu Trek is a remote and adventurous journey in the Annapurna region, taking you into the hidden valleys of Nar and Phu near the Tibetan border. This trek offers breathtaking landscapes, ancient monasteries, and stone villages with strong Tibetan culture. Surrounded by towering peaks like Kang Guru and Himlung Himal, the route is both scenic and culturally rich. Nar phu Trek is perfect for the trekkers looking for a quiet, less crowded experience.

            This trek offers a perfect mix of raw Himalayan landscapes, high mountain passes like Kang La (5,320 m), and deep cultural immersion. Along the way, you'll pass through ancient Tibetan-style villages, colorful prayer flags, and old monasteries that reflect the Buddhist culture of the region. The trail also provides breathtaking views of peaks like Annapurna II, Gangapurna, and Himlung Himal. Nar Phu Trek is very remote and less explored, the trek is best suited for experienced and physically fit trekkers.
                    `,
        ],
    };

    const note = {
        paragraphs: [
            `This trek reaches elevations above 5,000 meters, so proper acclimatization is essential. Follow your guide’s advice and take rest days as planned.`,

            `The Kang La Pass is steep and requires early morning ascents; carry warm layers and trekking poles for safety.`,
        ],
    };


    const briefing = {
        paragraphs: [
            `Before your departure, we will organize a short online or in-person briefing session where you’ll meet your trek leader and get insights into the itinerary, gear checklist, altitude tips, and cultural dos and don’ts. This ensures a well-informed and safe trekking experience.`,
        ],
    };



    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional Guide and Porters, their food, accommodation and insurance etc.",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 Meals per day which includes Breakfast, Lunch and Dinner.",
                "Medicines and water purifiers will be provided.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "Permits and any other necessary documents.",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government and service tax.",
            ],
        },
    ];



    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink not included in the provided 3 meals per day.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal trekking equipment (sleeping bag, trekking shoes, headlamp, crampons, etc.).",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the guide and porter.",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additional costs due to unforeseen circumstances (illness, weather, natural calamities, etc.).",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses not explicitly mentioned in the included section.",
            ],
        },
    ];

    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,324 m)" },
        { day: 2, title: "Drive from Kathmandu to Koto (2,620 m) [~7 hrs drive, ↑ 1,296 m]" },
        { day: 3, title: "Trek from Koto to Meta (3,560 m) [14.5 km, ~7–8 hrs, ↑ 940 m]" },
        { day: 4, title: "Meta to Kyang (3,840 m) [~10 km, ~5 hrs, ↑ 280 m]" },
        { day: 5, title: "Kyang to Phu (4,110 m) [~8–9 km, ~5–6 hrs, ↑ 270 m]" },
        { day: 6, title: "Acclimatization day at Phu" },
        { day: 7, title: "Phu to Nar Phedi (3,490 m) [~9 km, ~6–7 hrs, ↓ 620 m]" },
        { day: 8, title: "Nar Phedi to Nar Village (4,110 m) [~5 km, ~3 hrs, ↑ 620 m]" },
        { day: 9, title: "Nar to Ngawal via Kang La Pass (5,320 m) → (3,650 m) [~14 km, ~6–7 hrs, ↓ 460 m]" },
        { day: 10, title: "Ngawal to Manang (3,519 m) [~10 km, ~5–6 hrs, ↓ 131 m]" },
        { day: 11, title: "Drive from Manang to Kathmandu (1,324 m) [~11–12 hrs, ↓ 2,195 m]" },
        { day: 12, title: "Final Departure from Kathmandu" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Alex Morgan",
            role: "Adventure Photographer",
            company: "Trek Lens",
            rating: 5,
            content:
                "Nar Phu blew my mind. The landscapes are raw and spiritual, and the villages feel untouched by time. Kang La Pass was tough but absolutely worth it for the panoramic views.",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Sonam Sherpa",
            role: "Trekking Guide",
            company: "Himalaya Steps",
            rating: 5,
            content:
                "This trail offers something truly different. It’s quieter, more remote, and full of cultural gems. Phu village was the highlight for me — peaceful and full of history.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Maya Johnson",
            role: "Solo Traveler",
            company: "Wander Beyond",
            rating: 4,
            content:
                "If you're up for a challenge, this trek is for you. The steep ascents and remote trails were hard, but the authenticity of the experience made it unforgettable.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Kiran Thapa",
            role: "Cultural Enthusiast",
            company: "Nomad Nepal",
            rating: 5,
            content:
                "Nar and Phu villages are like stepping into a different era. The people, the monasteries, the high-altitude wilderness — it’s something you won’t find on the mainstream routes.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Lena Fischer",
            role: "Backpacker",
            company: "Soul Routes",
            rating: 5,
            content:
                "I’ve done Annapurna and Langtang before, but Nar Phu tops them in terms of remoteness and cultural immersion. Hidden, raw, and absolutely worth the effort.",
            avatar: "/images/review/5.png",
        },
    ];

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Nar Phu Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        Nar phu trek offers a perfect mix of raw Himalayan landscapes, high mountain passes like Kang La (5,320 m), and deep cultural immersion. Along the way, you&apos;ll pass through ancient Tibetan-style villages, colorful prayer flags, and old monasteries that reflect the Buddhist culture of the region. The trail also provides breathtaking views of peaks like Annapurna II, Gangapurna, and Himlung Himal. Nar Phu Trek is very remote and less explored, best suited for experienced and physically fit trekkers.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Nar Phu Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedNarPhuItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/map/trekking/nar-phu-trek.webp"
                            altText="Nar Phu Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={narPhuSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default NarPhuTrek
