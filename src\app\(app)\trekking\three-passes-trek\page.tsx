'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import Gallery<PERSON>ithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { detailedThreePassesItinerary } from '@/data/trekking/three-passes-trek/itinerary-detailed'
import { threePassesSection } from '@/data/trekking/three-passes-trek/package-info'

const ThreePassesTrek = () => {
    const router = useRouter();

    const customTrekData = {
        destination: "Lukla - Kathmandu (Covers Dhaulagiri Circuit)",
        accommodation: "Tea Houses",
        duration: "Approx 6–7 hrs daily/Can vary",
        maxElevation: "Kala Patthar (5,644 m)",
        group: "2–10 max",
        region: "Everest Region",
        type: "Unpaved and muddy",
        bestSeason: "March–May & September–November",
        grade: "Difficult",
    };


    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: "Explore the Buddhist and Tibetan culture of the region" },
        { text: "Reach at one of the highest mountain passes of Nepal Kang La Pass at 5,320 m." },
        { text: "The amazing landscape and exploration of diverse culture makes this journey more exciting" },
        { text: "Visit Nar Gompa and Phu Gompa in Nar Phu village" },
        { text: "Remote and Least Crowded unlike other trails" },
    ];


    const content = {
        paragraphs: [
            `The Three passes trek in Everest region is one of the most popular trekking routes in the world. Everest Region is always crowded with a lot of trekkers and climbers. This trail covers three passes i.e Renjo la (5,360 m), Cho la (5,420 m) and Kongma la (5,535 m) pass offering amazing landscapes and challenges to trekkers. The trail encompasses both Everest and Gokyo Lake trail. 

Along the trail, trekkers stop at famous sites, including the breathtaking view Gokyo Lakes, Everest Base Camp, and Kala Patthar viewpoint, which offers up-close views of Everest. The trail passes through traditional Sherpa villages like Namche Bazaar, Tengboche, and Thame. Explore the rich culture and history of mountaineering from sherpas.
                    `,
        ],
    };

    const note = {
        paragraphs: [
            `This trek involves crossing three high-altitude passes over 5,000 meters, so acclimatization and physical fitness are crucial. Altitude sickness prevention and gradual ascent are essential for safety.`,

            `The weather in the Everest region can change rapidly. Carry proper layered clothing, waterproof gear, and always listen to your guide’s instructions, especially during glacier crossings and pass ascents.`,
        ],
    };



    const briefing = {
        paragraphs: [
            `Before your departure, we’ll organize a detailed trip briefing either online or in-person. During this session, you’ll meet your trek leader and go over the itinerary, gear checklist, safety protocols, and altitude-related precautions. This ensures you are fully prepared for the Three Passes adventure.`,
        ],
    };


    const myInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Professional Guide and Potters, their food, accommodation and insurance etc.",
            ],
        },
        {
            title: "Meals & Drinks",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                "3 Meals per day which includes Breakfast, Lunch and Dinner.",
                "Medicines and water purifiers will be provided.",
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "Permits and any other necessary documents.",
            ],
        },
        {
            title: "Extras",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Government and service tax.",
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Any other food/drink should be covered by you, except (3 meals) provided by the company.",
            ],
        },
        {
            title: "Equipment",
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipments",
            ],
        },
        {
            title: "Tips",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for the Guide and Potter",
            ],
        },
        {
            title: "Unforeseen Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.",
            ],
        },
        {
            title: "Other",
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                "Any expenses that are not in the included section.",
            ],
        },
    ];

    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Arrival in Kathmandu (1,324 m)" },
        { day: 2, title: "Kathmandu to Pokhara (by bus or flight) " },
        { day: 3, title: "Fly from Kathmandu to Lukla and trek to Phakding (2,610 m) [6.2 km, ~3 hrs, ↑ 1,286 m]" },
        { day: 4, title: "Phakding to Namche Bazaar (3,440 m) [7.4 km, ~6 hrs, ↑ 830 m]" },
        { day: 5, title: "Namche to Marlung (4,249 m) [14 km, ~7–8 hrs, ↑ 809 m]" },
        { day: 6, title: "Marlung to Gokyo via Renjo La Pass (4,800 m) [10 km, ~6–7 hrs, ↑ 551 m]" },
        { day: 7, title: "Acclimatization day at Gokyo" },
        { day: 8, title: "Gokyo to Dzongla via Cho La Pass (4,830 m) [20 km, ~8–9 hrs, ↑ 30 m]" },
        { day: 9, title: "Dzongla to Lobuche (4,910 m) [8.3 km, ~4–5 hrs, ↑ 80 m]" },
        { day: 10, title: "Lobuche to Everest Base Camp and back to Gorak Shep (5,164 m) [15 km, ~7 hrs, ↑ 254 m]" },
        { day: 11, title: "Gorak Shep to Kala Patthar (5,644 m) and trek to Lobuche (4,910 m) [5 km, ~2–3 hrs, ↓ 254 m]" },
        { day: 12, title: "Lobuche to Chhukung via Kongma La Pass (4,730 m) [10.5 km, ~7–8 hrs, ↓ 180 m]" },
        { day: 13, title: "Chhukung to Dingboche (4,410 m) [4.5 km, ~3 hrs, ↓ 320 m]" },
        { day: 14, title: "Rest day at Dingboche" },
        { day: 15, title: "Dingboche to Tengboche (3,860 m) [10.5 km, ~5–6 hrs, ↓ 550 m]" },
        { day: 16, title: "Tengboche to Namche (3,440 m) [6 km, ~5–6 hrs, ↓ 420 m]" },
        { day: 17, title: "Namche Bazaar to Lukla (2,860 m) [13.5 km, ~6 hrs, ↓ 580 m]" },
        { day: 18, title: "Fly from Lukla to Kathmandu (1,324 m) [~35 min, ↓ 1,536 m]" },
        { day: 19, title: "Final Departure from Kathmandu" },
    ];


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Daniel Carter",
            role: "Alpine Trekker",
            company: "Global Peaks",
            rating: 5,
            content:
                "Three Passes Trek is the most complete trek I’ve ever done in Nepal. Each pass was a new challenge, and the views from Renjo La and Cho La were mind-blowing. Highly recommended for adventure lovers!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Tsering Lama",
            role: "Expedition Leader",
            company: "Sherpa Trails",
            rating: 5,
            content:
                "If you want a route that combines Everest Base Camp, Gokyo Lakes, and untouched trails — this is the one. The route is tough but the experience is unmatched in the Khumbu.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Laura Mitchell",
            role: "Photographer",
            company: "WanderFrame",
            rating: 4,
            content:
                "I loved the diversity — remote passes, beautiful lakes, iconic peaks, and vibrant Sherpa villages. Kala Patthar sunrise was the best moment of my entire trip.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Prabin Shrestha",
            role: "Trekking Enthusiast",
            company: "Hike Nepal",
            rating: 5,
            content:
                "Crossing Kongma La was the toughest day but incredibly rewarding. Seeing Everest from so many different angles over the days was something I’ll never forget.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Sophie Maier",
            role: "Adventure Blogger",
            company: "Trek Nomads",
            rating: 5,
            content:
                "This trek tested my limits but gave me memories for life. From snowy passes to spiritual monasteries, it was an adventure that balanced raw nature with rich culture.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image5.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Three Passes Trek"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="4 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        The Three Passes Trek in the Everest region is one of Nepal’s most thrilling and scenic trekking routes. This high-altitude adventure covers the iconic Renjo La (5,360 m), Cho La (5,420 m), and Kongma La (5,535 m) passes, offering dramatic views and demanding challenges. Along the trail, you&apos;ll witness the serene Gokyo Lakes, reach Everest Base Camp, and hike up Kala Patthar for breathtaking views of Mount Everest. The route also passes through vibrant Sherpa villages like Namche Bazaar, Thame, and Tengboche, giving you an immersive cultural experience in the heart of the Himalayas.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Three Passes Trek – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={detailedThreePassesItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/map/trekking/three-passes-trek.webp"
                            altText="Three Passes Trek Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={threePassesSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default ThreePassesTrek
