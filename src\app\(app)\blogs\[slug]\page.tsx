import React from 'react';
import { notFound } from 'next/navigation';
import { blogPosts } from '@/data/blog';
import BlogDetail from '@/modules/blogs/templates/blog-detail';

interface PageProps {
    params: Promise<{
        slug: string;
    }>;
}

const BlogPage: React.FC<PageProps> = async ({ params }) => {
    const { slug } = await params;

    const blog = blogPosts.find(post => post.slug === slug);

    if (!blog) {
        notFound();
    }

    return <BlogDetail blog={blog} />;
};

export default BlogPage;