'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import Gallery<PERSON>ithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { annapurnafoothillstrailrunning } from '@/data/trailrunning/annapurna-circuit/itinerary-detailed-footprints'
import { annapurnaFoothillsSection } from '@/data/trailrunning/annapurna-circuit/foothills-package'

const AnnapurnaFootHillsTrailRunning = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Pokhara - Tatopani',
        accommodation: 'Lodge and Tea Houses',
        duration: 'Approx 6-7 hrs daily/Can vary',
        maxElevation: '(2874 m)',
        group: '6',
        region: 'Annapurna Region',
        type: 'Mixture of Paved and Unpaved paths',
        bestSeason: 'March to May and September to November',
        grade: 'Easy - Moderate',
    };

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Panoramic mountain view from Peace pagoda stupa.' },
        { text: 'Amazing Landscape from Panchase view point.' },
        { text: 'Rejuvenate at Jhinu Danda\'s natural hot spring, soaking away your trail fatigue' },
        { text: 'Rhododendron blooms around Ghorepani during this season.' },
        { text: 'Amazing view of Mountains from Australian Base Camp.' },
        { text: 'Explore the culturally enriched village mostly inhabited by Gurungs around Pokhara valley.' },
    ];

    const content = {
        paragraphs: [
            `The 6-day trail running program offers an unforgettable journey through some of the most culturally rich villages around the outskirts of the Pokhara Valley. The route is a dynamic mix of paved, unpaved, and muddy paths, taking you across diverse terrain—from flat stretches to steep ascents and descents. With the stunning backdrop of the Himalayan mountain ranges, every step of the run promises to be both challenging and inspiring.

    The program starts at Pokhara and concludes in Tatopani, the trail weaves through iconic locations such as Bhadure, Jhinu Danda, Australian Base Camp, and Ghorepani. On average, runners will cover 15 to 20 kilometers each day, spending around 3 to 4 hours on the trail—though the duration may vary depending on individual pace and fitness levels.

    This trail running program covers the outskirts of Pokhara valley and is easy- Moderate Level. Anyone with little experience of running and hiking can easily complete this trail running program. There is no extreme uphill climb that will possess a huge physical challenge. The accommodations at the villages we stay at are very decent with facilities available.`,
        ],
    };


    const note = {
        paragraphs: [
            `The Annapurna Foothills Trail begins from Pokhara and involves daily running across varying terrain. Arrive in Pokhara at least a day before to get ready and acclimatize if needed.`,
            `This trail running route is suitable for beginner to intermediate runners, though it includes some challenging climbs like Mulde View Point (2874 m).`,
        ],
    };


    const briefing = {
        paragraphs: [
            `Once your booking is confirmed, we will organize an online or in-person briefing session to discuss the daily itinerary, safety guidelines, terrain expectations, and essential preparations for the Annapurna Foothills Trail Run.`,
        ],
    };


    const myInclusions: Category[] = [
        {
            title: 'Guides',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'One highly experienced, helpful, and friendly guide, and his food, accommodation, salary, equipment, and accidental insurance.',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                'We provide 3 meals per day (Breakfast, Lunch, and Dinner) and Tea / Coffee twice daily.',
                'Fresh/Dry fruits along the trek.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All the necessary paperwork and Annapurna conservation entry permit ( ACAP permit & TIMS card etc.)',
            ],
        },
        {
            title: 'Porters',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Porter for each 2 person'],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with an oximeter.',
                'Trek achievement certificate after the trek.',
                'Water bottle to store hot water overnight.',
                'Government taxes, TDS, VAT & other legal documents.',
            ],
        },
    ];


    const myExclusions: Category[] = [
        {
            title: 'Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any other food/drink should be covered by you, except (3 meals) provided by the company.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: ['Personal equipments'],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: ['Tips for the Guide and Potter'],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Additionally cost due to any unforeseen circumstances i.e illness, bad weather, natural calamities etc.',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: ['Any expenses that are not in the included section.'],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Pokhara to Jhuma Danda (1 500 m), 16 km, approx 3–4 hrs [↑ 678 m]" },
        { day: 2, title: "Jhuma Danda to Bhadaure via Panchase (1 600 m), 18.5 km, approx 4 hrs [↑ 100 m]" },
        { day: 3, title: "Bhadaure to Jhinu Danda via Australian Base Camp and Landruk (1 780 m), 22 km, approx 4–5 hrs [↑ 180 m]" },
        { day: 4, title: "Jhinu Danda to Tadapani (2 610 m), 9 km, approx 2–3 hrs [↑ 830 m]" },
        { day: 5, title: "Tadapani to Ghorepani via Mulde View Point (2 874 m), 14.5 km, approx 3–4 hrs [↑ 264 m]" },
        { day: 6, title: "Ghorepani to Patichaur (1 190 m), 25 km, approx 5–6 hrs [↓ 1 684 m], Return to Pokhara (Jeep/Bus, approx 3 hrs)" },
    ];

    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Ramesh Thapa",
            role: "Recreational Trail Runner",
            company: "Pokhara Running Club",
            rating: 5,
            content:
                "This trail was exactly what I needed — challenging but not overwhelming. The views from Panchase and Mulde were next-level. Loved the hot spring at Jhinu Danda!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Emma Liu",
            role: "Nature Enthusiast",
            company: "Trail Seekers Asia",
            rating: 5,
            content:
                "Running through Gurung villages and rhododendron forests was a spiritual experience. The logistics and support team were excellent. Highly recommend!",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Diego Martinez",
            role: "Adventure Vlogger",
            company: "Offbeat Paths",
            rating: 4,
            content:
                "Well-organized and beautiful! Ghorepani to Mohare was my favorite stretch. Just wish I had trained more for the Tadapani climb — it was intense but rewarding!",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Sujata Lama",
            role: "Running Coach",
            company: "Himalayan Trail Hub",
            rating: 5,
            content:
                "I brought a small group of first-time trail runners, and everyone finished strong. The terrain variety made it fun, and the team’s coordination was on point.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Tom Becker",
            role: "Travel Writer",
            company: "Global Stride",
            rating: 5,
            content:
                "Best way to see the real Nepal. Cultural immersion, physical challenge, and jaw-dropping landscapes all rolled into one seamless adventure.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image2.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Annapurna Foothills Trail Running"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program covers the outskirts of Pokhara valley and is easy- Moderate Level. Anyone with little experience of running and hiking can easily complete this trail running program. There is no extreme uphill climb that will possess a huge physical challenge. The accommodations at the villages we stay at are very decent with facilities available.
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Mansalu Circuit – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={annapurnafoothillstrailrunning} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Annapurna Foothills Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={annapurnaFoothillsSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default AnnapurnaFootHillsTrailRunning