'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, Car, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { kapucheKoriSection } from '@/data/trailrunning/kapuche-kori/kapuche-kori-section'
import { kapucheKoriItinerary } from '@/data/trailrunning/kapuche-kori/detailed-itinerary'

const KapucheKoriTrailRunningPage = () => {
    const router = useRouter();

    const customTrekData = {
        destination: 'Chipli - Pokhara',
        accommodation: 'Homestay and Teahouses',
        duration: 'Approx 4–5 hrs daily',
        maxElevation: 'Around 4100 m',
        group: '6',
        region: 'Annapurna Region',
        type: 'Unpaved and Muddy paths',
        bestSeason: 'March to May and September to November',
        grade: 'Moderate (Contains steep uphill and downhill)',
    }

    const handleInquireNow = () => {
        router.push('/');
    };

    const handleDatesPrice = () => {
        router.push('/');
    };

    const handleBookNow = () => {
        router.push('/');
    };

    const items: HighlightItem[] = [
        { text: 'Visit the world’s lowest glacier lake — Kapuche Lake at 2,546 m.' },
        { text: 'Panoramic views of the Himalayas from Kori Danda (3,800 m).' },
        { text: 'Explore the rich Gurung culture and museum in Sikles village.' },
        { text: 'Run through pristine forests with birdsong and flowing rivers.' },
        { text: 'Challenging trail with steep elevation gains through remote terrain.' },
        { text: 'Community homestays and traditional mountain hospitality.' },
    ]

    const content = {
        paragraphs: [
            `This trail running program takes you to the world’s lowest glacier lake at 2,546 meters and up to Kori at 3,800 meters. It's become a popular route because of the breathtaking views it offers in just a few days of running. From the lowest glacier lake to the towering mountains around it, the scenery is truly unforgettable—a once-in-a-lifetime experience. 

            Running in the steep uphill and downhill, inside the lush forest with the sound of bird chirping makes this trail running program challenging and exciting. You will also explore the traditional culture of Sikles village, a very famous village for homestay and offers scenic views of Mountains.

            The trail running program starts from chipli and ends at pokhara covering various areas like sikles, kori, Kapuche etc. The trail is mostly a mixed unpaved and muddy path through your journey.
                `,
        ],
    }

    const note = {
        paragraphs: [
            `The Manaslu Circuit Trail begins with an 8–9 hour drive from Kathmandu to Machha Khola. Arrive in Kathmandu at least a day before to prepare.`,
            `This trail running involves challenging terrains above 5000 meters, suitable for those experienced in high-altitude treks.`,
        ],
    }

    const briefing = {
        paragraphs: [
            `After confirming your booking, we'll hold an online briefing to explain itinerary details, safety measures, and necessary preparations.`,
        ],
    }


    const myInclusions: Category[] = [
        {
            title: 'Transportation',
            icon: <Car className="w-5 h-5 text-dark" />,
            items: [
                'Drive from Pokhara to Chipli and return from Sikles to Pokhara via jeep.',
                'Water bottle to store hot water overnight.',
            ],
        },
        {
            title: 'Accommodations',
            icon: <Hotel className="w-5 h-5 text-dark" />,
            items: [
                'Homestays in Chipli and Sikles, and teahouse lodgings in Kori and Hugu Goth.',
                'One highly experienced, helpful, and friendly guide, including their food, accommodation, salary, and insurance.',
            ],
        },
        {
            title: 'Food',
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                '3 meals per day (Breakfast, Lunch, and Dinner) and Tea/Coffee twice daily.',
                'Fresh and dry fruits provided during the trek.',
            ],
        },
        {
            title: 'Guides & Porters',
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                'Porter support for every 2 participants.',
            ],
        },
        {
            title: 'Permits & Paperwork',
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                'All necessary permits: Annapurna Conservation Area Permit (ACAP) & TIMS card.',
            ],
        },
        {
            title: 'Extras',
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                'First Aid Medical Kit Box with oximeter.',
                'Trek achievement certificate after the trek.',
                'Government taxes, TDS, VAT, and other legal documents.',
            ],
        },
    ];

    const myExclusions: Category[] = [
        {
            title: 'Food & Drinks',
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                'Any extra food or drink not covered in the included 3 meals/day.',
            ],
        },
        {
            title: 'Equipment',
            icon: <Backpack className="w-5 h-5 text-dark" />,
            items: [
                'Personal equipment (running shoes, clothes, backpack, etc.)',
            ],
        },
        {
            title: 'Tips',
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                'Tips for the guide and porter (customary).',
            ],
        },
        {
            title: 'Unforeseen Costs',
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                'Any additional costs due to illness, bad weather, natural calamities, or changes in itinerary.',
            ],
        },
        {
            title: 'Other',
            icon: <Ban className="w-5 h-5 text-dark" />,
            items: [
                'Any expenses not explicitly listed in the inclusion section.',
            ],
        },
    ];


    const galleryItems = [
        { src: "/images/fastpacking/hero/image1.webp", alt: "Group at Annapurna Base Camp" },
        { src: "/images/fastpacking/hero/image2.webp", alt: "Sunrise over the mountains" },
        { src: "/images/fastpacking/hero/image3.webp", alt: "Village along the trail" },
        { src: "/images/fastpacking/hero/image4.webp", alt: "Forest path" },
        { src: "/images/fastpacking/hero/image5.webp", alt: "Muktinath Temple" },
        { src: "/images/fastpacking/hero/image6.webp", alt: "Jomsom Village" }
    ]

    const shortItinerary: DaySummary[] = [
        { day: 1, title: "Drive Pokhara → Chipli (1580 m), approx 3 hrs [↑ 758 m]" },
        { day: 2, title: "Trail Run Chipli → Sikles via Tara Hilltop (2000 m), 12 km, approx 3–4 hrs [↑ 420 m]" },
        { day: 3, title: "Trail Run Sikles → Hugu Goth (~2100 m), 10.5 km, approx 3–4 hrs [↑ 100 m]" },
        { day: 4, title: "Trail Run Hugu → Kapuche → Kori (3800 m), 12.5 km, approx 4 hrs [↑ 1700 m]" },
        { day: 5, title: "Explore Kori → Thullake (4100 m) and back, 8 km, approx 5 hrs [↑ 300 m]" },
        { day: 6, title: "Trail Run Kori → Sikles (2000 m), 10.5 km, approx 5–6 hrs [↓ 2100 m]" },
        { day: 7, title: "Drive Sikles → Pokhara (822 m), 36 km, approx 3 hrs [↓ 1178 m]" },
    ]


    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: "Joy at ABC", youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Poon Hill Sunrise", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Thorung La Crossing", youtubeId: "mfQ31ybmPuA" },
        { id: "4", title: "Tilicho Lake", youtubeId: "rmuuxRaCSH0" },
        { id: "5", title: "Muktinath Temple", youtubeId: "sRxrwjOtIag" },
        { id: "6", title: "Jomsom Village", youtubeId: "ycE7bUq3-2k" },
    ]

    const myReviews: Review[] = [
        {
            id: 1,
            name: "Amit Kharel",
            role: "Endurance Coach",
            company: "Nepal Mountain Athletics",
            rating: 5,
            content:
                "The Manaslu trail is a hidden gem. The terrain, the altitude, and the peaceful routes offer the perfect blend of challenge and serenity for seasoned runners. Logistics were handled brilliantly!",
            avatar: "/images/review/1.png",
        },
        {
            id: 2,
            name: "Jenny Park",
            role: "Ultra Runner",
            company: "Seoul Trail Club",
            rating: 5,
            content:
                "Absolutely breathtaking! Running through remote Himalayan villages with stunning backdrops was surreal. The guides were knowledgeable, and the support team made everything smooth.",
            avatar: "/images/review/2.png",
        },
        {
            id: 3,
            name: "Carlos Mendes",
            role: "Adventure Blogger",
            company: "TrailNomads",
            rating: 4,
            content:
                "Challenging yet rewarding! The Larke Pass push was intense but totally worth it. A bit more pre-trip communication would’ve helped, but the overall experience was fantastic.",
            avatar: "/images/review/3.png",
        },
        {
            id: 4,
            name: "Sita Sharma",
            role: "Fitness Enthusiast",
            company: "HikeNepal",
            rating: 5,
            content:
                "The physical prep paid off! I’ve never felt more alive than crossing 5,000+ meters under my own power. Highly recommend this run for anyone ready to test their limits.",
            avatar: "/images/review/4.png",
        },
        {
            id: 5,
            name: "Leo Martin",
            role: "Expedition Planner",
            company: "WildStride",
            rating: 5,
            content:
                "Perfectly organized from start to finish. The scenery, the support crew, the cultural insights—everything combined into a life-changing mountain experience.",
            avatar: "/images/review/5.png",
        },
    ];


    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc="/images/fastpacking/hero/image2.webp"
                    imageAlt="Annapurna Circuit Fastpacking"
                    title="Trail Running in the Heart of the Himalayas: From Kapuche to Kori"
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration="11 Days"
                                originalPrice="USD 2000"
                                currentPrice="USD 1889 pp"
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <div>
                            <TrekInfo trekData={customTrekData} />
                        </div>

                        <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                        <div>
                            <div className="prose prose-lg max-w-none">
                                <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                    <p className="text-justify">
                                        This trail running program covers steep uphill and downhill which can be difficult for someone with very little to no running experience. The accommodation type is the combination of homestays (Chipli, Sikles) and teahouses (Kori, Hugu goth). The route from Sikles towards Hugu Goth can be difficult during the rainy season (Landslide prone area during heavy rainfall, Otherwise the route is safe and easy).
                                    </p>
                                </div>
                            </div>
                        </div>



                        <Highlight items={items} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Overview
                            content={content}
                            note={note}
                            briefing={briefing}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItinerarySummary
                            title={`Mansalu Circuit – ${shortItinerary.length} Days Short Itinerary`}
                            days={shortItinerary}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <GalleryWithMore items={galleryItems} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <ItineraryDetailed days={kapucheKoriItinerary} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Mapandchart 
                            imageSrc="/images/fastpacking/annapurna-circuit/map.webp"
                            altText="Kapuche to Kori Trail Running Route Map showing the complete trek with key landmarks, villages, and elevation points"
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TrekInclusionsExclusions
                            inclusions={myInclusions}
                            exclusions={myExclusions}
                        />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <Gears />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        {/* <PreperationandTraining /> */}
                        {/* <hr className="w-full h-px bg-dark/30 border-0 mb-4" /> */}

                        <PackageInformation sections={kapucheKoriSection} />
                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                        <TravelersReview
                            videos={sampleVideos}
                            onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                        />

                        <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        <StackedReviews
                            reviews={myReviews}
                            headerTitle="Customer Success Stories"
                        // headerIcon={SomeOtherIcon}
                        // containerHeightVH={80}
                        // stickyTopClass="top-32"
                        />
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default KapucheKoriTrailRunningPage