"use client"

import { useState } from "react"
import Image from "next/image"
import dynamic from "next/dynamic"
import "react-image-lightbox/style.css"
import { ImagesIcon } from "lucide-react"

const Lightbox = dynamic(() => import("react-image-lightbox"), {
  ssr: false,
})

interface Item {
  src: string
  alt: string
}

interface GalleryWithMoreProps {
  items: Item[]
}

export default function GalleryWithMore({ items }: GalleryWithMoreProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [photoIndex, setPhotoIndex] = useState(0)
  const previewCount = 3

  const openAt = (index: number) => {
    setPhotoIndex(index)
    setIsOpen(true)
  }

  return (
    <div className="container mx-auto px-4 ">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-brand rounded-full flex items-center justify-center">
          <ImagesIcon size={16} className="text-light" />
        </div>
        <h2 className="text-2xl md:text-3xl font-bold text-brand">
          Photos
        </h2>
      </div>

      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {items.slice(0, previewCount).map((item, i) => (
          <div
            key={i}
            className="relative w-full aspect-square rounded-lg overflow-hidden cursor-pointer"
            onClick={() => openAt(i)}
          >
            <Image
              src={item.src}
              alt={item.alt}
              fill
              className="object-cover"
            />
          </div>
        ))}

        {items.length > previewCount && (
          <div
            className="relative w-full aspect-square rounded-lg overflow-hidden cursor-pointer"
            onClick={() => openAt(previewCount)}
          >
            <Image
              src={items[previewCount].src}
              alt={items[previewCount].alt}
              fill
              className="object-cover brightness-50"
            />
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
              <span className="text-xl font-semibold">
                +{items.length - previewCount}
              </span>
              <span className="text-sm">View More</span>
            </div>
          </div>
        )}
      </div>

      {isOpen && (
        <Lightbox
          mainSrc={items[photoIndex].src}
          nextSrc={items[(photoIndex + 1) % items.length].src}
          prevSrc={
            items[(photoIndex + items.length - 1) % items.length].src
          }
          onCloseRequest={() => setIsOpen(false)}
          onMovePrevRequest={() =>
            setPhotoIndex((photoIndex + items.length - 1) % items.length)
          }
          onMoveNextRequest={() =>
            setPhotoIndex((photoIndex + 1) % items.length)
          }
          imageCaption={items[photoIndex].alt}
          imagePadding={20}
        />
      )}
    </div>
  )
}
