import React from 'react';
import Image from 'next/image';

interface TrekData {
    destination?: string;
    accommodation?: string;
    duration?: string;
    maxElevation?: string;
    group?: string;
    region?: string;
    mealsIncluded?: string;
    bestSeason?: string;
    grade?: string;
    type?: string;
}

interface TrekInfoProps {
    trekData?: TrekData;
}

const TrekInfo: React.FC<TrekInfoProps> = ({ trekData }) => {
    const defaultData = {
        accommodation: 'Guest House/ Lodge/camping',
        mealsIncluded: '(Breakfast, Lunch, and Dinner) during the trek',
        bestSeason: 'March to May and September to November',
        grade: 'Challenging'
    };

    const data = { ...defaultData, ...trekData };

    const trekDetails = [
        {
            icon: '/images/trek-icon/location.png',
            title: 'Start/End',
            value: data.destination,
            iconAlt: 'Destination icon'
        },
        {
            icon: '/images/trek-icon/icon-tent.svg',
            title: 'Accommodation',
            value: data.accommodation,
            iconAlt: 'Accommodation icon'
        },
        {
            icon: '/images/trek-icon/icon-travel.svg',
            title: 'Trail Type',
            value: data.type,
            iconAlt: 'Type icon'
        },
        {
            icon: '/images/trek-icon/cloud.png',
            title: 'Best Season',
            value: data.bestSeason,
            iconAlt: 'Season icon'
        },
        {
            icon: '/images/trek-icon/grade-icon.svg',
            title: 'Grade',
            value: data.grade,
            iconAlt: 'Grade icon'
        },
        {
            icon: '/images/trek-icon/calendar.png',
            title: 'Duration',
            value: data.duration,
            iconAlt: 'Duration icon'
        },
    ];

    return (
        <div className="bg-light rounded-lg items-center ">
            <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {trekDetails.map((detail, index) => (
                    <div key={index} className="flex flex-col space-y-1">
                        <div className="flex flex-col items-left space-x-3">
                            <div className="w-8 h-8 relative flex-shrink-0">
                                <Image
                                    src={detail.icon}
                                    alt={detail.iconAlt}
                                    width={32}
                                    height={32}
                                    className="w-full h-full object-contain"
                                />
                            </div>
                            <h3 className="text-sm font-light text-dark/80 tracking-wide">
                                {detail.title}
                            </h3>
                        </div>

                        <p className="text-dark leading-relaxed">
                            {detail.value}
                        </p>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default TrekInfo;